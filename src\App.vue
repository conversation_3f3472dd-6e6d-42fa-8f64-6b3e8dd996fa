<template>
  <div id="app">
    <div class="scale-wrapper" :style="`width: ${$pageWidth}px; height: ${$pageHeight}px;`">
      <router-view></router-view>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import {csdnLogin} from "@/api/csdnIndexApi";
export default {
  name: 'App',
  data () {
    return {
      scaleWidth: 1,
      scaleHeight: 1,
      $pageWidth: 7680,
      $pageHeight: 2160,
    }
  },
  watch: {
    '$route.path': {
      handler (newVal, oldVal) {
        // if (this.markerArray && this.markerArray.length > 0) {
        //   top.mapUtil.removeAllLayers(this.markerArray)
        //   this.CLEAR_MARKER()
        // }
        if (window.view) {
          top.mapUtil.removeAllLayers(['事件点位MarkerArray', '预警点位MarkerArray'])
        }
        this.changePage()
      }
    },
    '$pageWidth': function () {
      this.changePage()
    }
  },
  computed: {
    // ...mapState({
    //   markerArray: state => state.mapStatus.markerArray,
    // }),
    ...mapGetters(['markerArray'])
  },
  created () {
    // this.loginCsdn()
    this.changePage()
  },
  mounted () {
    this.$nextTick(() => {
      this.resetPageSize()
      window.addEventListener('resize', this.resetPageSize)
    })
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.resetPageSize)
  },
  methods: {
    ...mapMutations('mapStatus', ['CLEAR_MARKER']),

    mainTitleClick () {
      this.$router.push("/")
    },
    changePage () {
      this.resetPageSize()
    },
    resetPageSize () {
      const container = document.getElementById('app')
      this.scaleWidth = container.clientWidth / this.$pageWidth
      this.scaleHeight = container.clientHeight / this.$pageHeight
    },
    loginCsdn() {
      csdnLogin(JSON.stringify({username: this.$jquery.trim("liyun"), password: this.encode64(this.$jquery.trim("liyun@123"))})).then(res => {
        console.log('接口数据', res)
        if (res.data.code == '200') {
          sessionStorage.setItem('csdnIndexApiToken', res.data.portToken)
          sessionStorage.setItem('csdnIndexApiAuthorization', res.data.token)
          sessionStorage.setItem('csdnIndexApiRole', res.data.userId)
        }
      })
    },
    encode64: function (val) {
      // base64加密开始
      var keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='
      var output = ''
      var chr1,
        chr2,
        chr3 = ''
      var enc1,
        enc2,
        enc3,
        enc4 = ''
      var i = 0
      do {
        chr1 = val.charCodeAt(i++)
        chr2 = val.charCodeAt(i++)
        chr3 = val.charCodeAt(i++)
        enc1 = chr1 >> 2
        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4)
        enc3 = ((chr2 & 15) << 2) | (chr3 >> 6)
        enc4 = chr3 & 63
        if (isNaN(chr2)) {
          enc3 = enc4 = 64
        } else if (isNaN(chr3)) {
          enc4 = 64
        }
        output = output + keyStr.charAt(enc1) + keyStr.charAt(enc2) + keyStr.charAt(enc3) + keyStr.charAt(enc4)
        chr1 = chr2 = chr3 = ''
        enc1 = enc2 = enc3 = enc4 = ''
      } while (i < val.length)
      return output
    },
  }
}
</script>

<style>
* {
  padding: 0;
  margin: 0;
}

li {
  list-style: none;
}

#app {
  height: 100%;
}

.scale-wrapper {
  transform-origin: left top;
  background: url("@/assets/img/bkg_img.png") 0 0 no-repeat;
  background-size: cover;
  height: 100%;
  width: 100%;
}</style>
