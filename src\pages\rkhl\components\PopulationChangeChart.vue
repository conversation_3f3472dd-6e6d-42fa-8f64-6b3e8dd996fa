<template>
  <div class="chart-item">
    <SubTitle title="实时人口变化趋势" />
    <div class="chart-container">
      <div class="chart-controls">
        <span class="control-btn active">月</span>
        <span class="control-btn">天</span>
        <span class="control-btn">时</span>
      </div>
      <div id="populationChangeChart" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'

export default {
  name: 'PopulationChangeChart',
  components: {
    SubTitle,
  },
  mounted() {
    this.initPopulationChangeChart()
  },
  methods: {
    // 实时人口变化趋势图表
    initPopulationChangeChart() {
      const chart = this.$echarts.init(document.getElementById('populationChangeChart'))
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          textStyle: { color: '#fff', fontSize: 14 },
        },
        grid: {
          left: '10%',
          right: '10%',
          top: '20%',
          bottom: '20%',
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月'],
          axisLabel: { color: '#fff', fontSize: 12 },
          axisLine: { lineStyle: { color: '#333' } },
        },
        yAxis: {
          type: 'value',
          axisLabel: { color: '#fff', fontSize: 12 },
          axisLine: { lineStyle: { color: '#333' } },
          splitLine: { lineStyle: { color: '#333' } },
        },
        series: [
          {
            type: 'line',
            data: [1200, 1100, 1300, 1000, 1400, 1200, 1500, 1300, 1600],
            smooth: true,
            lineStyle: { color: '#ffc460', width: 2 },
            itemStyle: { color: '#ffc460' },
            areaStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(255, 196, 96, 0.3)' },
                { offset: 1, color: 'rgba(255, 196, 96, 0)' },
              ]),
            },
          },
        ],
      }
      chart.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart-controls {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      gap: 8px;
      z-index: 10;

      .control-btn {
        width: 120px;
        height: 40px;
        font-size: 24px;
        font-weight: 400;
        color: #bfbcbc;
        background: rgba(0, 74, 166, 0.3);
        border: 2px solid #215293;
        text-align: center;
        line-height: 40px;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          color: #ffffff;
          font-weight: 700;
          background: rgba(0, 74, 166, 0.8);
        }

        &:hover {
          background: rgba(0, 74, 166, 0.6);
          color: #ffffff;
        }
      }
    }

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>
