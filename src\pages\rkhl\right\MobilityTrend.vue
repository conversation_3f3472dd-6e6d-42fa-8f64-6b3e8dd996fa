<template>
  <div class="chart-item">
    <SubTitle title="流动趋势" />
    <div class="chart-container">
      <div id="mobilityTrend" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'

export default {
  name: 'MobilityTrend',
  components: { SubTitle },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      const chart = this.$echarts.init(document.getElementById('mobilityTrend'))
      const hours = ['0时','3时','6时','9时','12时','15时','18时','21时']
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          textStyle: { color: '#fff', fontSize: 14 },
        },
        legend: { data: ['流入', '流出'], textStyle: { color: '#fff' } },
        grid: { left: '10%', right: '10%', top: '18%', bottom: '12%' },
        xAxis: {
          type: 'category',
          data: hours,
          axisLabel: { color: '#fff', fontSize: 12 },
          axisLine: { lineStyle: { color: '#333' } },
        },
        yAxis: {
          type: 'value',
          axisLabel: { color: '#fff', fontSize: 12 },
          axisLine: { lineStyle: { color: '#333' } },
          splitLine: { lineStyle: { color: '#333' } },
        },
        series: [
          {
            name: '流入',
            type: 'line',
            smooth: true,
            data: [50, 60, 80, 120, 160, 140, 130, 90],
            lineStyle: { color: '#00c0ff', width: 2 },
            itemStyle: { color: '#00c0ff' },
          },
          {
            name: '流出',
            type: 'line',
            smooth: true,
            data: [40, 45, 55, 100, 140, 150, 160, 120],
            lineStyle: { color: '#ff6a6a', width: 2 },
            itemStyle: { color: '#ff6a6a' },
          },
        ],
      }
      chart.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>

