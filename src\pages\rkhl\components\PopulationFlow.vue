<template>
  <div class="chart-item">
    <SubTitle title="人员流入流出情况" />
    <div class="chart-container">
      <div id="populationFlow" class="chart"></div>
    </div>
  </div>
</template>

<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import SubTitle from '@/components/SubTitle.vue'

export default {
  name: 'PopulationFlow',
  components: {
    SubTitle,
  },
  mounted() {
    this.initPopulationFlow()
  },
  methods: {
    // 人员流入流出情况
    initPopulationFlow() {
      // 参考 changSan 方法中 name 为 '日' 的默认值实现
      getCsdnInterface1('csrk_lrlcqsdb', { day: 7 }).then((res) => {
        let xdata = [],
          datanow = [],
          data7 = []
          console.log('res.data.data',res);
        res.data.data.map((ele) => {
          xdata.push(ele.sort_time.slice(5))
          datanow.push((ele.in_count / 10000).toFixed(2))
          data7.push((ele.out_count / 10000).toFixed(2))
        })
        this.getLineCharts('populationFlow', xdata, datanow, data7)
      })
    },

    // 人员流入流出对比图表绘制
    getLineCharts(dom, xdata, datanow, data7) {
      let myEc = this.$echarts.init(document.getElementById(dom))
      const option = {
        grid: {
          left: '5%',
          right: '6%',
          top: '20%',
          bottom: '1%',
          containLabel: true,
        },
        legend: {
          data: ['流入', '流出'],
          top: '5%',
          icon: 'circle',
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 30,
          textStyle: {
            color: '#fff',
            fontSize: 26,
            fontFamily: 'SourceHanSansCN-Medium',
          },
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          textStyle: {
            fontSize: 26,
            color: '#fff',
          },
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
        },
        xAxis: [
          {
            type: 'category',
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 9,
              textStyle: {
                color: '#fff',
                fontSize: 26,
              },
            },
            data: xdata,
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位：万人',
            min: function (value) {
              return (value.min - 5).toFixed(0)
            },
            nameTextStyle: {
              color: '#fff',
              fontSize: 26,
              padding: [0, -30, 0, 0],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                color: '#fff',
                fontSize: 26,
                fontFamily: 'SourceHanSansCN-Medium',
              },
            },
          },
        ],
        series: [
          {
            name: '流入',
            type: 'line',
            symbolSize: 10,
            itemStyle: {
              color: '#00C0FF',
              borderColor: '#00C0FF',
              borderWidth: 1,
            },
            data: datanow,
          },
          {
            name: '流出',
            type: 'line',
            symbolSize: 10,
            itemStyle: {
              color: '#FFC460',
              borderColor: '#FFC460',
              borderWidth: 1,
            },
            data: data7,
          },
        ],
      }

      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart-controls {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      gap: 8px;
      z-index: 10;

      .control-btn {
        width: 120px;
        height: 40px;
        font-size: 24px;
        font-weight: 400;
        color: #bfbcbc;
        background: rgba(0, 74, 166, 0.3);
        border: 2px solid #215293;
        text-align: center;
        line-height: 40px;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          color: #ffffff;
          font-weight: 700;
          background: rgba(0, 74, 166, 0.8);
        }

        &:hover {
          background: rgba(0, 74, 166, 0.6);
          color: #ffffff;
        }
      }
    }

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>
