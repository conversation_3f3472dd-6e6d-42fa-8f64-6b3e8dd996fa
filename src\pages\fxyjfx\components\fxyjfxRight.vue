<!--
 * @Description: 产业链右侧组件
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-09-18 14:08:20
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-19 09:14:38
-->
<template>
  <div class="container"></div>
</template>

<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
export default {
  name: 'fxyjfxRight',
  data() {
    return {}
  },

  created() {
    this.getData()
  },

  mounted() {},

  beforeDestroy() {},

  methods: {},
}
</script>

<style scoped lang="less">
</style>