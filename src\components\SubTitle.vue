<template>
  <div class="sub-title">
    {{ title }}
    <img 
      v-if="showIcon" 
      :src="iconSrc" 
      class="title-icon" 
      @click="handleIconClick" 
    />
  </div>
</template>

<script>
export default {
  name: 'SubTitle',
  props: {
    title: {
      type: String,
      required: true,
      default: ''
    },
    showIcon: {
      type: Boolean,
      default: false
    },
    iconSrc: {
      type: String,
      default: '@/assets/img/dyCyl/zczcNew.png'
    }
  },
  methods: {
    handleIconClick() {
      this.$emit('icon-click')
    }
  }
}
</script>

<style lang="less" scoped>
.sub-title {
  width: 100%;
  height: 90px;
  background-image: url('@/assets/img/dyCyl/ejbt.png');
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40px;
  color: #ffffff;
  position: relative;
}

.title-icon {
  position: absolute;
  top: 26px;
  right: 66px;
  width: 40px;
  height: 44px;
  cursor: pointer;
}
</style>
