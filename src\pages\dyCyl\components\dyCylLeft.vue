<template>
  <div class="container">
    <div class="titleWrap">
      <div class="title_l">
        <div class="title col-white">{{ basicInfo.cylmc }}</div>
      </div>
      <div class="header-r">
        <div
          class="tab_item"
          v-for="(item, index) in tabList"
          @click="tabChange(index)"
          :class="tabIndex == index ? 'active' : ''"
          :key="index"
        >
          <span class="tfam" :class="tabIndex == index ? 'col-white' : 'col-gren'">{{ item }}</span>
        </div>
      </div>
    </div>

    <div class="header">
      <div class="header-l" v-show="tabIndex == 0">
        产业节点
        <count-to
          class="number s-c-yellow-gradient s-w7 newT"
          :start-val="0"
          :end-val="jd_num"
          :duration="1000"
        ></count-to>
        个
        <span style="margin-left: 100px"></span>
        企业数量
        <count-to
          class="number s-c-yellow-gradient s-w7 newT"
          :start-val="0"
          :end-val="qy_num"
          :duration="1000"
        ></count-to>
        家
      </div>

      <div class="header-c" v-show="tabIndex == 0">
        <div v-for="(item, index) in legend" class="htabClass" :key="index">
          <span
            class="icon"
            :style="{ background: item.color2, border: '1px solid ' + item.color1, 'border-radius': '5px' }"
          ></span>
          {{ item.name }}
          <div class="countClass" v-show="cylId == 104">
            {{ item.count }}
            <div class="unitCount">个</div>
          </div>
        </div>
      </div>
    </div>

    <div class="con">
      <div class="con-item" v-for="(item, index) in list" v-show="tabIndex == 0 && cylId != 104" :key="index">
        <div class="con-title">
          <span class="s-c-blue-gradient s-font-45 s-w7">{{ item.name }}</span>
          <span>
            <span class="s-c-yellow-gradient s-m-l-30 s-m-r-10 newT">{{ item.jd }}</span>
            个节点
            <span class="s-c-yellow-gradient s-m-l-30 s-m-r-10 newT">{{ item.qy }}</span>
            家企业
          </span>
        </div>
        <div class="chart_box">
          <div :id="'chart1_' + index" style="width: 1040px; height: 1420px"></div>
        </div>
      </div>

      <div class="newChartWrap" v-show="tabIndex == 0 && cylId == 104">
        <div class="chartNewClass" id="chartNew"></div>
      </div>

      <div class="screenWrap" v-show="tabIndex == 1">
        <!-- 地图背景 -->
        <div class="map-bgnuo"></div>
        <!-- 地图 -->
        <div class="mapnuo" ref="myEchartnuo"></div>
        <!-- 地图描述 -->
        <div class="mapDesClass" v-show="cylId == 103 && showMapDesc"></div>
      </div>

      <div class="screenWrap positionClass" v-show="tabIndex == 2">
        <!-- 招商地图内容 -->
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import * as echarts from 'echarts'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

// 全局变量声明，和 Center.vue 保持一致
const baseURL = window.baseURL || { url: '' }

export default {
  name: 'DyCylLeft',
  data() {
    return {
      title: '',
      jd_num: 0,
      qy_num: 0,
      legend: [
        {
          name: '断链节点',
          color1: '#F06060',
          color2: '#4D3958',
          count: '',
        },
        {
          name: '弱链节点',
          color1: '#0091FF',
          color2: '#074C8C',
          count: '',
        },
        {
          name: '强链节点',
          color1: '#13C2C2',
          color2: '#0E5D7B',
          count: '',
        },
      ],
      list: [
        {
          name: '上游',
          jd: 0,
          qy: 0,
        },
        {
          name: '中游',
          jd: 0,
          qy: 0,
        },
        {
          name: '下游',
          jd: 0,
          qy: 0,
        },
      ],
      cylId: 101,
      tabIndex: 0,
      tabList: ['产业链图谱', '产业地图', '招商地图'],
      myChartMapnuo: null,
      popData: [],
      popData3: [
        {
          name: '婺城区',
          value: [119.502579, 29.000607, 0],
        },
        {
          name: '金东区',
          value: [119.781264, 29.105635, 0],
        },
        {
          name: '兰溪市',
          value: [119.360521, 29.210065, 0],
        },
        {
          name: '东阳市',
          value: [120.33234, 29.222506, 0],
        },
        {
          name: '义乌市',
          value: [120.074311, 29.306463, 0],
        },
        {
          name: '永康市',
          value: [120.036328, 28.855293, 0],
        },
        {
          name: '浦江县',
          value: [119.893363, 29.551254, 0],
        },
        {
          name: '武义县',
          value: [119.819159, 28.796503, 0],
        },
        {
          name: '磐安县',
          value: [120.54113, 28.952227, 0],
        },
        {
          name: '开发区',
          value: [119.652577, 29.082627, 0],
        },
      ],
      basicInfo: {},
      showMapDesc: false,
      chartData0: [],
      chartData1: [],
      chartData2: [],
    }
  },
  computed: {
    height_2() {
      switch (this.cylId) {
        case 104:
          return '990px'
        case 103:
          return '990px'
        default:
          return '1530px'
      }
    },
  },
  mounted() {
    let that = this
    that.title = '2222'
    // 从路由参数中获取id，如果没有则默认为104
    that.cylId = this.$route.query.id ? parseInt(this.$route.query.id) : 104
    that.init1()
    this.getBsicInfo()
    if (this.tabIndex == 1) {
      this.initData()
    }
  },
  watch: {
    // 监听路由变化，当id参数改变时重新获取数据
    '$route.query.id'(newId) {
      if (newId) {
        this.cylId = parseInt(newId)
        this.init1()
        this.getBsicInfo()
        if (this.tabIndex == 1) {
          this.initData()
        }
      }
    }
  },
  methods: {
    init1() {
      this.queryNum()
      if (this.cylId == 104) {
        this.querychartData()
        this.statisticsData()
      } else {
        this.querychartDataOld()
      }
    },

    tabChange(index) {
      this.tabIndex = index
      if (index == 1) {
        // this.initData()
      }
    },

    queryNum() {
      if (this.cylId !== 109) {
        // 109人工智能产业链不用这个接口
        getCsdnInterface1('csdn_qyhx21', { ywwd2: this.cylId }).then((res) => {
          const data = (res && res.data && res.data.data) || []
          this.list.forEach((item) => {
            const found = data.find((el) => el.ywwd1 == item.name)
            item.qy = found ? found.qy_num || 0 : 0
            item.jd = found ? found.jd_num || 0 : 0
          })
        })
        getCsdnInterface1('csdn_qyhx22', { ywwd2: this.cylId }).then((res) => {
          const d = (res && res.data && res.data.data) || []
          this.jd_num = d[0] ? d[0].jd_num || 0 : 0
          this.qy_num = d[0] ? d[0].qy_num || 0 : 0
        })
      } else {
        getCsdnInterface1('csdn_qyhx38', { ywwd2: this.cylId }).then((res) => {
          const data = (res && res.data && res.data.data) || []
          this.list.forEach((item) => {
            const f = data.find((el) => el.location == item.name)
            item.qy = f ? f.qy_sum || 0 : 0
            item.jd = f ? f.jd_sum || 0 : 0
          })
        })
        getCsdnInterface1('csdn_qyhx40', { ywwd2: this.cylId }).then((res) => {
          const d = (res && res.data && res.data.data) || []
          this.jd_num = d[0] ? d[0].jd_num || 0 : 0
          this.qy_num = d[0] ? d[0].qy_num || 0 : 0
        })
      }
    },

    querychartDataOld() {
      getCsdnInterface1('csdn_qyhx1', { sstp: this.cylId }).then((res) => {
        const data = (res && res.data && res.data.data) || []
        getCsdnInterface1('csdn_qyhx20', { ywwd2: this.cylId }).then((el) => {
          const elData = (el && el.data && el.data.data) || []
          if (this.cylId !== 109) {
            // 109人工智能产业链不用这个接口的tjz
            data.forEach((item) => {
              item.count = 0
              elData.forEach((element) => {
                if (item.ej == element.ywwd1) {
                  item.count = Number(element.tjz)
                }
              })
            })
          }
          let data1 = data.filter((item) => {
            return item.pid == 0
          })
          let data2 = data.filter((item) => {
            return item.pid != 0
          })

          let result = []
          data1.forEach((item1) => {
            let bCount = 0
            let obj1 = {
              name: item1.ej,
              type: item1.aCount == 1 ? 1 : item1.aCount > 1 ? 2 : 0, // 0:弱链 1:强链 2:断链
              aCount: item1.aCount,
              location: item1.location,
              children: [],
            }
            data2.forEach((item2) => {
              if (item1.id == item2.pid) {
                let obj2 = {
                  name: item2.ej,
                  type: item2.count == 1 ? 1 : item2.count > 1 ? 2 : 0, // 0:断链 1:弱链 2:强链
                  num: item2.count,
                }
                obj1.children.push(obj2)
                bCount += item2.count
              }
            })
            obj1.type = bCount == 1 ? 1 : bCount > 1 ? 2 : 0 // 0:断链 1:弱链 2:强链
            result.push(obj1)
          })

          this.chartData0 = result.filter((item) => {
            return item.location == '上游'
          })
          this.chartData1 = result.filter((item) => {
            return item.location == '中游'
          })
          this.chartData2 = result.filter((item) => {
            return item.location == '下游'
          })

          this.$nextTick(() => {
            this.getchart('chart1_0', this.chartData0, 0)
            this.getchart('chart1_1', this.chartData1, 1)
            this.getchart('chart1_2', this.chartData2, 2)
          })
        })
      })
    },

    querychartData() {
      getCsdnInterface1('csdn_lqzf_cly', { sscly: this.cylId }).then((res) => {
        const data = (res && res.data && res.data.data) || []
        let treeList = this.listToTree(data)
        this.$nextTick(() => {
          this.getchartnewTwo('chartNew', treeList)
        })
      })
    },

    statisticsData() {
      getCsdnInterface1('qyhx_cyl_qrdlds', { sscly: this.cylId }).then((res) => {
        const data = (res && res.data && res.data.data) || []
        data.forEach((el) => {
          if (el.ldlx == '强链') {
            this.legend[2].count = el.tjz
          } else if (el.ldlx == '弱链') {
            this.legend[1].count = el.tjz
          } else if (el.ldlx == '断链') {
            this.legend[0].count = el.tjz
          }
        })
      })
    },

    handleData(list) {
      let newlist = list.map((item) => {
        let innerChildren = []
        if (item.children && item.children.length > 0) {
          innerChildren = this.handleData(item.children)
        }
        return {
          name: item.ld_name,
          value: item.jdqys,
          jdqys: item.jdqys,
          ld_code: item.ld_code,
          ldcj: item.ldcj,
          ldlx: item.ldlx,
          parent_code: item.parent_code,
          sscly: item.sscly,
          children: innerChildren,
        }
      })
      return newlist
    },

    getchartnewTwo(id, chartData) {
      const myChart = echarts.init(document.getElementById(id))
      const newData = this.handleData(chartData)
      const option = {
        tooltip: {
          trigger: 'item',
          triggerOn: 'mousemove',
          formatter: (p) => `${p.data.name} ${p.data.value}`,
          textStyle: { color: '#fff', fontSize: 36 },
          backgroundColor: '#041b34',
          borderColor: '#035693',
          borderWidth: 8,
          padding: [20, 30, 20, 30],
        },
        dataZoom: { type: 'inside' },
        series: [
          {
            type: 'tree',
            roam: true,
            initialTreeDepth: 1,
            data: newData,
            top: '0%',
            left: '20%',
            bottom: '0%',
            right: '0%',
            symbolSize: 50,
            zoom: 0.8,
            edgeShape: 'polyline',
            symbol: (value, params) => {
              let icon = ''
              if (params.data.children.length < 1) icon = ''
              else if (params.collapsed) icon = `image://data:image/png;base64,iVBORw0K...`
              else icon = `image://data:image/png;base64,iVBORw0K...`
              return icon
            },
            itemStyle: { color: 'rgba(255,255,255,0)' },
            symbolOffset: [20, 0],
            label: {
              normal: {
                position: 'left',
                verticalAlign: 'middle',
                distance: -2,
                overflow: 'truncate',
                ellipsis: '...',
                color: '#fff',
                formatter: (p) => {
                  let str = `${p.data.name} ${p.data.value == 0 ? '' : p.data.value}`
                  if (str.length > 8) str = str.slice(0, 8) + '...'
                  let menu = `{d|${str}}`
                  if (p.data.ldlx == '断链') menu = `{a|${str}}`
                  else if (p.data.ldlx == '弱链') menu = `{b|${str}}`
                  else if (p.data.ldlx == '强链') menu = `{c|${str}}`
                  return menu
                },
                rich: {
                  a: {
                    fontSize: 36,
                    width: 360,
                    height: 80,
                    color: '#fff',
                    align: 'middle',
                    backgroundColor: '#492c42',
                    borderRadius: 10,
                    overflow: 'truncate',
                    ellipsis: '...',
                    borderWidth: 3,
                    borderColor: '#e85e5f',
                  },
                  b: {
                    fontSize: 36,
                    width: 360,
                    height: 80,
                    color: '#fff',
                    align: 'middle',
                    backgroundColor: '#04457c',
                    borderRadius: 10,
                    overflow: 'truncate',
                    ellipsis: '...',
                    borderWidth: 3,
                    borderColor: '#026dc1',
                  },
                  c: {
                    fontSize: 36,
                    width: 360,
                    height: 80,
                    color: '#fff',
                    align: 'middle',
                    backgroundColor: '#095269',
                    borderRadius: 10,
                    overflow: 'truncate',
                    ellipsis: '...',
                    borderWidth: 3,
                    borderColor: '#11aaaf',
                  },
                  d: {
                    fontSize: 36,
                    width: 360,
                    height: 80,
                    color: '#fff',
                    align: 'middle',
                    backgroundColor: '#395372',
                    borderRadius: 10,
                    overflow: 'truncate',
                    ellipsis: '...',
                    borderWidth: 3,
                    borderColor: '#bad3ff',
                  },
                },
              },
            },
            lineStyle: { color: 'rgba(255,255,255,.5)', width: 5, curveness: 0.5 },
            expandAndCollapse: true,
            animationDuration: 550,
            animationDurationUpdate: 750,
          },
        ],
      }
      myChart.setOption(option)

      myChart.on('mousedown', (e) => {
        const name = e.data.name
        const nodeList = myChart._chartsViews[0]._data.tree._nodes
        const curNode = nodeList.filter((item) => item.name === name && item.children.length == e.data.children.length)
        const dataIndex = curNode[0].dataIndex
        const curIsExpand = JSON.parse(JSON.stringify(curNode[0].isExpand))
        if (e.event.topTarget.style.text) {
          nodeList.forEach((item) => {
            if (item.dataIndex == dataIndex && item.name == name && !curIsExpand) item.isExpand = true
            else if (item.dataIndex == dataIndex && item.name == name && curIsExpand) item.isExpand = false
          })
          this.queryQyList(name)
        }
      })
    },

    getchart(id, chartData, index) {
      const myChart = echarts.init(document.getElementById(id))
      chartData.forEach((item) => {
        item.itemStyle = { color: this.legend[item.type].color2, borderColor: this.legend[item.type].color1 }
        if (item.children) {
          item.children.forEach((el) => {
            el.itemStyle = { color: this.legend[el.type].color2, borderColor: this.legend[el.type].color1 }
          })
        }
      })
      const data = { name: '', lineStyle: { width: 0 }, children: chartData }
      const format = (name) => (name.length > 6 ? name.slice(0, 6) + '...' : name)
      const option = {
        series: [
          {
            type: 'tree',
            initialTreeDepth: -1,
            data: [data],
            top: '0%',
            left: '-30%',
            bottom: '0%',
            right: '25%',
            symbolSize: [300, 50],
            edgeShape: 'polyline',
            symbol: 'rect',
            label: {
              normal: {
                verticalAlign: 'middle',
                align: 'center',
                fontSize: 32,
                color: '#fff',
                borderRadius: [0, 0, 5, 5],
                formatter: (p) => (p.data.num ? `${format(p.data.name)}(${p.data.num})` : format(p.data.name)),
              },
            },
            lineStyle: { color: 'rgba(255,255,255,0.8)', width: 3, curveness: 0.5 },
            expandAndCollapse: true,
            animationDuration: 550,
            animationDurationUpdate: 750,
          },
        ],
      }
      myChart.setOption(option)
      myChart.on('click', (params) => {
        if (!params.data.children) this.queryQyList(params.name)
      })
    },

    queryQyList(name) {
      if (window.parent && window.parent.lay && window.parent.lay.openIframe) {
        const that = this
        window.parent.lay.openIframe({
          type: 'openIframe',
          name: 'gf-qy-list',
          src: baseURL.url + '/static/citybrain/qyhx/commont/gf-qy-list.html',
          left: 'calc(50% - 1554px)',
          top: '190px',
          width: '3108px',
          height: '1900px',
          zIndex: '500',
          argument: { status: 'qyList', name, cylId: that.cylId },
        })
      }
    },

    getGeoJon(code, name) {
      axios({ method: 'get', url: `https://geo.datav.aliyun.com/areas_v3/bound/${code}.json` }).then((res) => {
        this.myChartMapnuo = echarts.init(this.$refs.myEchartnuo).dispose()
        this.myChartMapnuo = echarts.init(this.$refs.myEchartnuo)
        echarts.registerMap(name, res.data)
        this.showMapDesc = false
        setTimeout(() => {
          this.initEcharts(name, res.data)
        }, 500)
      })
    },

    initEcharts(registname, registdata) {
      this.myChartMapnuo.resize()
      this.$nextTick(() => {
        const option = {
          geo: [
            {
              show: true,
              map: registname,
              zoom: registname == '中国' ? 1.3 : 1.2,
              roam: false,
              zlevel: 5,
              layoutCenter: ['50%', '50%'],
              layoutSize: '90%',
              aspectScale: 0.85,
              itemStyle: { areaColor: 'transparent' },
              silent: true,
            },
            {
              show: true,
              map: registname,
              zoom: registname == '中国' ? 1.3 : 1.2,
              roam: false,
              zlevel: 4,
              layoutCenter: ['50.4%', '50%'],
              layoutSize: '90%',
              aspectScale: 0.85,
              itemStyle: {
                borderWidth: 1,
                borderColor: 'rgba(22, 186, 212,0.8)',
                shadowColor: 'rgba(80, 183, 140,0.5)',
                shadowOffsetY: 5,
                shadowBlur: 15,
                areaColor: 'rgba(5,21,35,0.1)',
              },
              silent: true,
            },
            {
              show: true,
              map: registname,
              zoom: registname == '中国' ? 1.3 : 1.2,
              roam: false,
              zlevel: 3,
              layoutCenter: ['50.6%', '50%'],
              layoutSize: '90%',
              aspectScale: 0.85,
              itemStyle: {
                borderWidth: 6,
                borderColor: 'rgba(29,111,165,1)',
                shadowColor: 'rgba(29,111,165,0.5)',
                shadowOffsetY: 15,
                shadowBlur: 8,
                areaColor: 'rgba(5,21,35,0.8)',
              },
              silent: true,
            },
            {
              show: true,
              map: registname,
              zoom: registname == '中国' ? 1.3 : 1.2,
              roam: false,
              zlevel: 3,
              layoutCenter: ['50.8%', '50%'],
              layoutSize: '90%',
              aspectScale: 0.85,
              itemStyle: {
                borderWidth: 6,
                borderColor: 'rgba(29,111,165,1)',
                shadowColor: 'rgba(29,111,165,0.5)',
                shadowOffsetY: 15,
                shadowBlur: 8,
                areaColor: 'rgba(5,21,35,0.8)',
              },
              silent: true,
            },
            {
              show: true,
              map: registname,
              zoom: registname == '中国' ? 1.3 : 1.2,
              roam: false,
              zlevel: 3,
              layoutCenter: ['51%', '50%'],
              layoutSize: '90%',
              aspectScale: 0.85,
              itemStyle: {
                borderWidth: 6,
                borderColor: 'rgba(29,111,165,1)',
                shadowColor: 'rgba(29,111,165,0.5)',
                shadowOffsetY: 15,
                shadowBlur: 8,
                areaColor: 'rgba(5,21,35,0.8)',
              },
              silent: true,
            },
          ],
          series: [
            {
              type: 'map',
              map: registname,
              zoom: registname == '中国' ? 1.3 : 1.2,
              roam: false,
              layoutCenter: ['50%', '50%'],
              layoutSize: '90%',
              aspectScale: 0.85,
              selectedMode: false,
              label: { show: true, color: '#fff', position: 'inside', distance: 0, fontSize: 35 },
              itemStyle: {
                normal: {
                  areaColor: '#0c395d',
                  borderColor: '#56c4e3',
                  borderWidth: 2,
                  shadowBlur: 15,
                  shadowColor: 'rgb(58,115,192)',
                  shadowOffsetX: 7,
                  shadowOffsetY: 6,
                },
                emphasis: { color: '#000', areaColor: '#8dd7fc' },
              },
              zlevel: 99,
              data: [],
            },
            {
              type: 'effectScatter',
              coordinateSystem: 'geo',
              zlevel: 999,
              data: this.popData,
              symbolSize: 10,
              label: {
                normal: {
                  show: true,
                  formatter: (p) => `{fline|${p.name}}{tline|${p.value[2]}}`,
                  position: 'top',
                  backgroundColor: '#0a0b0b99',
                  padding: [0, 0],
                  borderRadius: 3,
                  fontSize: 32,
                  fontWeight: 500,
                  color: '#ffffff',
                  rich: {
                    fline: {
                      padding: [10, 10, 10, 10],
                      color: '#ffffff',
                      fontSize: 32,
                      width: 80,
                      align: 'center',
                      height: 40,
                      backgroundColor: '#ff7817',
                    },
                    tline: {
                      padding: [10, 10, 10, 10],
                      color: '#ffffff',
                      fontSize: 32,
                      lineHeight: 45,
                      width: 80,
                      height: 40,
                    },
                  },
                },
                emphasis: { show: true },
              },
              itemStyle: { color: '#ff7817' },
            },
          ],
        }
        this.myChartMapnuo.setOption(option)
        this.showMapDesc = true
      })
    },

    initData() {
      this.popData = []
      this.popData3.map((item) => (item.value[2] = 0))
      getCsdnInterface1('csdn_qyhx16', { ywwd2: this.cylId }).then((res) => {
        const data = (res && res.data && res.data.data) || []
        data.map((item) => {
          const t = this.popData3.find((el) => item.qxwd == el.name)
          if (t) t.value[2] = item.tjz
        })
        this.popData = this.popData3.filter((item) => item.value[2] != 0)
        this.getGeoJon('330700_full', '金华市')
      })
    },

    getBsicInfo() {
      getCsdnInterface1('csdn_qyhx44', { sstp: this.cylId }).then((res) => {
        const d = (res && res.data && res.data.data) || []
        this.basicInfo = d[0] || {}
      })
    },

    listToTree(list) {
      const map = {}
      const tree = []
      list.forEach((item) => {
        map[item.ld_code] = { ...item, children: [] }
      })
      list.forEach((item) => {
        const node = map[item.ld_code]
        if (item.parent_code && map[item.parent_code]) map[item.parent_code].children.push(node)
        else tree.push(node)
      })
      return tree
    },
  },
}
</script>

<style lang="less" scoped>
* {
  margin: 0;
  padding: 0;
}

[v-cloak] {
  display: none;
}

.container {
  width: 2886px;
  height: 1862px;
  padding: 10px 55px 0px;
  box-sizing: border-box;
  background: url('@/assets/img/dyCyl/cyl_new_bg.png') no-repeat;
  background-size: 100% 100%;
}

.title {
  /* height: 150px; */
  font-weight: bolder;
  font-size: 72px;
  /* line-height: 150px; */
  letter-spacing: 7px;
}

.info {
  display: flex;
  flex-wrap: wrap;
  font-size: 40px;
  margin: 40px 20px;
  line-height: 80px;
}

.info > div {
  width: 33%;
}

.info-name {
  color: #b7c2cb;
  font-size: 40px;
}

.info-value {
  color: #fff;
  font-size: 40px;
  font-weight: bolder;
}

.col-white {
  background: linear-gradient(90deg, #ffffff 0%, #bcecff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.col-gren {
  background: linear-gradient(90deg, #ffffff 0%, #cae2ff 73%, #8facd7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header {
  width: 100%;
  height: 150px;
  /* background: url('@/assets/img/dyCyl/title-bg.png') no-repeat;
  background-size: 100% 100%; */
  display: flex;
  justify-content: space-between;
  line-height: 150px;
  padding: 0px 50px;
  box-sizing: border-box;
}

.number {
  display: inline-block;
  font-size: 72px;
  margin: 0 10px;
}

.header-l {
  display: flex;
  font-size: 36px;
  color: #bad3ff;
}

.header-c {
  width: 916px;
  font-size: 36px;
  color: #bad3ff;
  display: flex;
  justify-content: space-evenly;
}

.header-c .icon {
  width: 26px;
  height: 26px;
  display: inline-block;
  margin-right: 10px;
}

.header-r {
  display: flex;
  align-items: center;
  text-align: center;
}

.header-r > img {
  cursor: pointer;
  margin-top: 35px;
}

.con {
  display: flex;
  justify-content: space-evenly;
  height: 1520px;
  margin-top: 20px;
  font-size: 36px;
  color: #bad3ff;
}

.con-item {
  /* width: 900px; */
  width: 925px;
  height: 100%;
  background: rgba(26, 85, 191, 0.12);
}

.con-title {
  width: 100%;
  height: 91px;
  line-height: 91px;
  background: url(@/assets/img/dyCyl/title-2.png) no-repeat;
  background-size: 100% 100%;
  padding: 0 85px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  font-size: 36px;
}

.chart_box {
  height: calc(100% - 90px);
  overflow-y: scroll;
  overflow-x: hidden;
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 10px;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
  border-radius: 5px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(35, 144, 207, 0.4);
}

.tab_item {
  width: 314px;
  height: 75px;
  background: url('@/assets/img/dyCyl/btn2.png') no-repeat;
  background-size: 100% 100%;
  font-size: 46px;
  text-align: center;
  line-height: 75px;
  cursor: pointer;
}

.active {
  background: url('@/assets/img/dyCyl/btn-active2.png') no-repeat;
  background-size: 100% 100%;
}

.ccWrap {
  display: flex;
  align-items: center;
}

.wOne {
  /* background-color: #a0e3ff; */
  width: 560px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  /*text-align: center; */
}

.tfam {
  font-family: YouSheBiaoTiHei;
  /* font-weight: bolder; */
}

.newT {
  background: linear-gradient(180deg, #ffc460 0%, #fd852e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/********************************** 大挪移页面 **********************************/

.screenWrap {
  width: 100%;
  height: 100%;
  /* background-color: #BAD3FF; */
  position: relative;
}

.positionClass {
  top: -106px;
  left: 0px;
}

.testBg {
  width: 2799px;
  height: 1557px;
  background-color: #bad3ff;
  background: url('@/assets/img/dyCyl/zsdt.png') no-repeat;
  background-size: 100% 100%;
  /* position: absolute;
  top: 0px;
  left: 0px; */
}

.count {
  width: 1460px;
  height: 220px;
  background: url('@/assets/img/dyCyl/count_bg.png') no-repeat 0px -148px;
  position: absolute;
  /* top: 240px; */
  top: 50px;
  left: 50px;
  /* margin:120px 0 0 554px; */
  display: flex;
  justify-content: space-evenly;
  font-size: 40px;
  color: #fff;
  font-weight: 500;
  text-align: center;
  line-height: 80px;
  padding: 25px 0;
  box-sizing: border-box;
}

.col_b {
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
  font-size: 65px;
  /* font-family: 'DIN-Bold'; */
}

.mapnuo {
  position: absolute;

  /* width: 1700px;
  height: 1350px;
  top: 610px;
  left: 438px; */

  width: calc(1700px * 0.8);
  height: calc(1350px * 0.8);
  /* width: calc(1400px * 0.88);
  height: calc(1400px * 0.88); */
  top: 122px;
  left: 682px;
}

.map-bgnuo {
  position: absolute;
  background: url('@/assets/img/dyCyl/map_bg.png') no-repeat;
  background-size: 100% 100%;

  /* width: 1886px;
  height: 945px;
  top: 915px;
  left: 355px; */

  width: calc(1886px * 0.9);
  height: calc(945px * 0.9);
  top: 605px;
  left: 537px;
}

.mapDesClass {
  width: 2755px;
  height: 1462px;
  position: absolute;
  top: 0px;
  left: 0px;
  background: url('@/assets/img/dyCyl/mapDescs.png') no-repeat;
  background-size: 100% 100%;
}

.box {
  width: calc(1188px * 0.8);
  height: 1150px;
  background: url('@/assets/img/dyCyl/box_bg.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  right: 0px;
  /* top: 180px; */
  top: 0px;
}

.box1 {
  /* top: 1050px; */
  /* top: 1145px; */
  top: 600px;
}

.box_top {
  width: 100%;
  height: 90px;
  background: url('@/assets/img/dyCyl/box_top_bg.png') no-repeat;
  background-size: 100% 100%;
}

.box_top > span {
  font-size: 48px;
  line-height: 90px;
  margin-left: 88px;
  font-weight: 700;
  background: linear-gradient(180deg, #ffffff 0%, #dcefff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.wNew {
  width: 66% !important;
}

.titleWrap {
  height: 150px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title_l {
  display: flex;
  align-items: center;
}

.titleImg {
  width: 60px;
  height: 60px;
  margin-left: 48px;
  cursor: pointer;
  margin-top: 9px;
}

/* 二级菜单样式 */
.el-popover {
  background-color: rgba(5, 36, 73, 1);
  width: 1688px;
  height: 600px;
  border: 3px solid rgba(5, 145, 243, 1);
  border-radius: 15px;
  padding: 60px 60px 60px 60px;
}

.contentClass {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(4, 1fr);
}

.gridInner {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.newChartWrap {
  width: 100%;
  height: 100%;
  position: relative;
}

.chartNewClass {
  width: 100%;
  height: 100%;
  /* background-color: rgba(255, 196, 96, .3); */
  /* cursor: pointer; */
}

.zoomBig {
  position: absolute;
  right: 332px;
  bottom: 40px;
  width: 80px;
  height: 80px;
  background: url('@/assets/img/dyCyl/big.png') no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
}

.zoomSmall {
  position: absolute;
  right: 168px;
  bottom: 40px;
  width: 80px;
  height: 80px;
  background: url('@/assets/img/dyCyl/small.png') no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
}

/****************************************************************** 图谱样式 ******************************************************************/

.nodeWrap {
  position: relative;
}

.txtClass {
  background-color: #395372;
  white-space: nowrap;
  border: 3px solid #bad3ff;
  padding: 8px 20px 8px 20px;
  font-size: 36px;
  border-radius: 10px;
}

.dotClass {
  position: absolute;
  right: 0;
  top: 50%;
  padding: 8px;
  border-radius: 50%;
  background: #04447a;
  border: 1px solid #0091ff;
  font-size: 22px;
  color: #ffffff;
  font-weight: bolder;
  width: 34px;
  height: 34px;
  transform: translate(72%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.htabClass {
  display: flex;
  align-items: center;
}

.countClass {
  display: flex;
  align-items: center;
  margin-left: 8px;
}

.unitCount {
  margin-left: 8px;
}

.tooltipClass {
  font-size: 36px;
  background-color: #041b34;
  border-radius: 10px;
  color: '#fff';
  padding: 20px 300px 200px 30px;
  /* display: flex;
  justify-content: center;
  align-items: center; */
}
</style>

