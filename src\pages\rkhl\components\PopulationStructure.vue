<template>
  <div class="chart-item">
    <SubTitle title="人员结构" />
    <div class="chart-container">
      <div class="structure-charts">
        <div id="genderChart" class="gender-chart"></div>
        <div id="ageChart" class="age-chart"></div>
      </div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'PopulationStructure',
  components: {
    SubTitle,
  },
  mounted() {
    this.fetchPopulationStructureData()
  },
  methods: {
    // 获取人员结构数据
    async fetchPopulationStructureData() {
      try {
        // 调用接口获取人员结构数据
        const response = await getCsdnInterface1('csrk_gznnbl')
        const data = response.data.data[0]

        // 初始化性别图表
        this.initGenderChart(Number(data.gender_male) * 100, Number(data.gender_female) * 100)

        // 初始化年龄结构图表
        const ageData = [
          { value: Number(data['18-24岁']) * 100, name: '18-24岁' },
          { value: Number(data['25-34岁']) * 100, name: '25-34岁' },
          { value: Number(data['35-44岁']) * 100, name: '35-44岁' },
          { value: Number(data['45岁以上']) * 100, name: '45岁以上' },
          { value: Number(data['其他']) * 100, name: '其他' },
        ]
        this.initAgeChart(ageData)
      } catch (error) {
        console.error('获取人员结构数据失败:', error)
        // 使用默认数据
        this.initGenderChart(62.1, 37.9)
        const defaultAgeData = [
          { value: 1048, name: '18-24岁' },
          { value: 735, name: '25-34岁' },
          { value: 580, name: '35-44岁' },
          { value: 484, name: '45岁以上' },
          { value: 200, name: '其他' },
        ]
        this.initAgeChart(defaultAgeData)
      }
    },

    // 性别结构图表
    initGenderChart(malePercent, femalePercent) {
      const chart = this.$echarts.init(document.getElementById('genderChart'))
      const bodyMax = 100 // 指定图形界限的值

      // 导入图片资源
      const menImg = require('@/assets/img/rkhl/men.png')
      const men2Img = require('@/assets/img/rkhl/men2.png')
      const womenImg = require('@/assets/img/rkhl/women.png')
      const women2Img = require('@/assets/img/rkhl/women2.png')

      const labelSetting = {
        normal: {
          show: true,
          position: 'bottom',
          offset: [0, -150],
          formatter: function (param) {
            return ((param.value / bodyMax) * 100).toFixed(1) + '%'
          },
          textStyle: {
            fontSize: 32,
            color: '#fff',
          },
          z: 20,
        },
      }

      const option = {
        tooltip: {
          show: false, // 鼠标放上去显示悬浮数据
        },
        grid: {
          top: '10%',
          left: '20%',
          bottom: '15%',
          containLabel: true,
        },
        xAxis: {
          data: ['男性', '女性'],
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
        },
        yAxis: {
          max: bodyMax,
          splitLine: {
            show: false,
          },
          axisTick: {
            // 刻度线
            show: false,
          },
          axisLine: {
            // 轴线
            show: false,
          },
          axisLabel: {
            // 轴坐标文字
            show: false,
          },
        },
        series: [
          {
            name: '',
            type: 'pictorialBar',
            symbolClip: true,
            symbolBoundingData: bodyMax,
            label: labelSetting,
            data: [
              {
                value: malePercent,
                symbol: 'image://' + men2Img,
                itemStyle: {
                  normal: {
                    color: '#2bbded', // 单独控制颜色
                  },
                },
              },
              {
                value: femalePercent,
                symbol: 'image://' + women2Img,
                itemStyle: {
                  normal: {
                    color: '#eac253', // 单独控制颜色
                  },
                },
              },
            ],
            z: 20,
          },
          {
            // 设置背景底色，不同的情况用这个
            name: 'full',
            type: 'pictorialBar', // 异型柱状图 图片、SVG PathData
            symbolBoundingData: bodyMax,
            animationDuration: 0,
            itemStyle: {
              normal: {
                color: '#ccc', // 设置全部颜色，统一设置
              },
            },
            z: 10,
            data: [
              {
                itemStyle: {
                  normal: {
                    color: 'rgba(68, 195, 234, 0.80)', // 单独控制颜色
                  },
                },
                value: 100,
                symbol: 'image://' + menImg,
              },
              {
                itemStyle: {
                  normal: {
                    color: 'rgba(242, 168, 102, 0.40)', // 单独控制颜色
                  },
                },
                value: 100,
                symbol: 'image://' + womenImg,
              },
            ],
          },
        ],
      }

      chart.setOption(option)
      chart.getZr().on('mousemove', () => {
        chart.getZr().setCursorStyle('default')
      })
    },

    // 年龄结构饼图
    initAgeChart(ageData) {
      const chart = this.$echarts.init(document.getElementById('ageChart'))

      let total = 0
      ageData.forEach(item => {
        total += item.value
      })

      const option = {
        color: ['#00c0ff', '#22e8e8', '#007ea7', '#ffc460', '#b673d6'],
        tooltip: {
          trigger: 'item',
          formatter: '{b}: <br/>{d}%',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          textStyle: {
            color: '#fff',
            fontSize: 14,
          },
        },
        legend: {
          type: 'plain',
          icon: 'circle',
          orient: 'vertical',
          right: '10%',
          top: 'center',
          itemGap: 15,
          itemWidth: 12,
          itemHeight: 12,
          textStyle: {
            color: '#fff',
            fontSize: 16,
          },
          formatter: function(name) {
            for (let i = 0; i < ageData.length; i++) {
              if (name === ageData[i].name) {
                return name + '  ' + ((ageData[i].value / total) * 100).toFixed(1) + '%'
              }
            }
          },
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['35%', '50%'],
            data: ageData,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
          },
        ],
      }
      chart.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .structure-charts {
      display: flex;
      width: 100%;
      height: 470px;
      gap: 20px;

      .gender-chart {
        width: 50%;
        height: 100%;
      }

      .age-chart {
        width: 50%;
        height: 100%;
      }
    }
  }
}
</style>
