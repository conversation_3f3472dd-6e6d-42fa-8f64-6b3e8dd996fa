<template>
  <div class="chart-item">
    <SubTitle title="外来人口特征" />
    <div class="chart-container">
      <div id="migrantPopulationFeatures" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'

export default {
  name: 'MigrantPopulationFeatures',
  components: { SubTitle },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      const chart = this.$echarts.init(document.getElementById('migrantPopulationFeatures'))
      const option = {
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          textStyle: { color: '#fff', fontSize: 14 },
        },
        radar: {
          indicator: [
            { name: '年龄', max: 100 },
            { name: '教育', max: 100 },
            { name: '行业', max: 100 },
            { name: '居住年限', max: 100 },
            { name: '家庭化', max: 100 },
          ],
          splitLine: { lineStyle: { color: '#333' } },
          splitArea: { areaStyle: { color: ['rgba(0,0,0,0)'] } },
          axisLine: { lineStyle: { color: '#333' } },
          name: { color: '#fff' },
        },
        series: [
          {
            type: 'radar',
            data: [
              {
                value: [70, 65, 60, 55, 50],
                areaStyle: { color: 'rgba(0,192,255,0.3)' },
                lineStyle: { color: '#00c0ff' },
                itemStyle: { color: '#00c0ff' },
              },
            ],
          },
        ],
      }
      chart.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>

