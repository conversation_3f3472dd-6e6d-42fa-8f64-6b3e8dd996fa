<template>
  <div class="qyhx-right-container">
    <div class="first-title">
      <div class="title-text">企业智评</div>
    </div>
    <div class="con" style="height: 600px; display: flex; align-items: flex-start">
      <!-- 智评分数 -->
      <div class="qyhxright-con-item" style="width: 30%">
        <div id="qyhx-chart01" style="width: 100%; height: 500px"></div>
        <p class="zbfs-css">{{ currentText }}</p>
      </div>
      <!-- 企业健康评级 -->
      <div class="qyhxright-con-item" style="position: relative; width: 70%; height: 100%">
        <!-- <div id="chart02" style="width: 100%; height: 600px"></div> -->
        <div class="qypj" style="height: 65%">
          <li v-for="(item, index) in qypjData" :key="index" :style="index == 2 ? 'cursor:unset' : 'cursor:pointer'">
            <img :src="item.icon" alt="" />
            <div class="qyhx-li-content">
              <p class="pj-text">{{ item.name }}</p>
              <p class="qyhx-line" style="width: 240px"></p>
              <p class="pj-value" v-if="index != 2">
                {{ index == 1 ? parseFloat(item.value).toFixed(1) + '%' : item.value }}
                <span style="font-size: 32px" v-if="index == 0">
                  <el-popover
                    placement="bottom"
                    width="450"
                    :title="item.name"
                    trigger="hover"
                    :content="`所处行业评级企业共${item.hy}家，本企业排名${item.qy}`"
                  >
                    <span slot="reference">
                      (
                      <span class="jz">{{ item.qy }}</span>
                      /{{ item.hy }})
                    </span>
                  </el-popover>
                </span>
              </p>
              <p class="pj-value" v-else>
                <img src="@/pages/qyzf/img/click-1.png" @click="visible = true" alt="" style="width: 60px; height: 50px; cursor: pointer" />
              </p>
            </div>
          </li>
        </div>
        <!-- 点击切换区域 -->
        <div class="qh-con">
          <div
            class="qyhxTab"
            :style="
              currentIndex == 0
                ? 'left:90px'
                : currentIndex == 1
                ? 'left:520px'
                : currentIndex == 2
                ? 'left:995px'
                : 'display:none'
            "
          ></div>
        </div>
        <div class="qypj-sm">
          <div class="sm-zs"></div>
          <div class="sm-wz">
            该企业健康评级得分:
            <!-- 该企业所处行业平均企业健康评级为 -->
            <span>{{ qyjkfs || '-' }}分</span>
            总评为{{ hypjpjHypj }}
          </div>
        </div>
      </div>
    </div>
    <div class="qyhxCon con-bottom" v-show="!isAllfb">
      <!-- 企业智评分数仪表盘 -->
      <div class="con-left">
        <div
          name="pie-chart"
          :id="`pie${index}`"
          :class="[index % 2 != 0 ? '' : 'blue-bg']"
          style="width: 414px; height: 200px"
          v-for="(item, index) in leftData"
          :key="index"
        ></div>
      </div>
      <!-- 企业基本属性 ||currentIndex==1-->
      <div class="con-right" v-if="currentIndex == null">
        <div :class="[index % 2 == 0 ? 'blue-bg' : '', 'qyhx-ul-list']" v-for="(item, index) in rightData" :key="index">
          <div class="qyhx-li-list" v-for="(ele, num) in rightData[index].value" :key="num">
            <div class="img">
              <img v-if="index == 0" :src="getRightImageSrc(num)" alt="" @error="handleImageError" />
              <img v-else-if="index == 1" :src="getRightImageSrc(num + 3)" alt="" @error="handleImageError" />
              <img v-else-if="index == 2" :src="getRightImageSrc(num + 6)" alt="" @error="handleImageError" />
              <img v-else-if="index == 3" :src="getRightImageSrc(num + 9)" alt="" @error="handleImageError" />
              <img v-else :src="getRightImageSrc(num + 9)" alt="" @error="handleImageError" />
            </div>
            <div class="qyhx-li-content">
              <p>{{ ele.name }}</p>
              <p class="qyhx-line"></p>
              <p>
                <span :class="['value-css', ele.unit == '-' ? 'color-size' : '']">
                  <span v-if="ele.value != '-' && Number.isNaN(Number(ele.value)) == false">
                    {{ Math.round(ele.value) }}
                  </span>
                  <span v-else>{{ ele.value }}</span>
                  <span style="font-size: 22px" v-if="ele.unit != '' && ele.unit != '-'">{{ ele.unit }}</span>
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- currentIndex!=1&& -->
      <div class="con-right" v-if="currentIndex != null">
        <div :class="[index % 2 == 0 ? 'blue-bg' : '', 'qyhx-ul-list']" v-for="(item, index) in rightData" :key="index">
          <img
            v-if="rightData[index].value.length > 1"
            src="@/pages/qyzf/img/to-left.png"
            class="left-a"
            @click="tab_to_prev(index)"
          />
          <el-carousel ref="tab" arrow="never" :autoplay="false" style="width: 100%">
            <el-carousel-item v-for="(x, k) in item.value" :key="k">
              <div class="qyhx-li-list" v-for="(ele, num) in x" :key="num">
                <div class="qyhx-img" v-if="rightImgShow && ele.strName">
                  <img :src="getImageSrc(ele.strName)" alt="" @error="handleImageError" />
                </div>
                <div class="qyhx-li-content">
                  <p>{{ ele.name }}</p>
                  <p class="qyhx-line"></p>
                  <p>
                    <span :class="['value-css', ele.unit == '-' ? 'color-size' : '']">
                      <span v-if="ele.value != '-' && typeof ele.value == 'number'">{{ Math.round(ele.value) }}</span>
                      <span>{{ ele.value }}</span>
                      <span style="font-size: 22px" v-if="ele.unit != '' && ele.unit != '-'">{{ ele.unit }}</span>
                    </span>
                  </p>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>

          <img
            v-if="rightData[index].value.length > 1"
            src="@/pages/qyzf/img/to-left.png"
            class="right-a"
            @click="tab_to_next(index)"
          />
        </div>
      </div>
    </div>
    <!-- 政策推荐弹窗 -->
    <div v-if="visible">
      <qyhxReport
        :visible="visible"
        :tyshxydm="allMessage[0].tyshxydm"
        @close="visible = false"
      ></qyhxReport>
    </div>
  </div>
</template>
<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import qyhxReport from '@/pages/qyzf/components/qyhxReportDialog'
export default {
  props: {
    allMessage: {
      type: Array,
      default: () => [],
    },
  },
  components: { qyhxReport },
  data() {
    return {
      visible:false,
      baseURL: {
        url: '',
      },
      pdfName: 'https://csdn.dsjj.jinhua.gov.cn:8101/static/citybrain/qyhx/pdf/健康诊断报告.pdf', //#view=FitH,top
      yelArr: [
        '资产负债',
        '经营风险',
        '企业人才资源占比',
        '企业信用风险预警评级',
        '企业净资产保值增值率',
        '企业环境信用评级',
        '上个月用气量同比变化率',
        '用工同比变化率',
      ], //黄色文字
      //评分仪表盘数据
      leftData: [
        {
          name: '规模指数',
          value: 0,
          strName: 'gmzs',
        },
        {
          name: '创新指数',
          value: 0,
          strName: 'cxzs',
        },
        {
          name: '成长指数',
          value: 0,
          strName: 'czzs',
        },
        {
          name: '荣誉指数',
          value: 0,
          strName: 'ryzs',
        },
        {
          name: '风险指数',
          value: 0,
          strName: 'fxzz',
        },
      ],
      //企业基本属性左侧数据
      qyzplData: [
        {
          name: '规模指数',
          value: 0,
          strName: 'gmzs',
        },
        {
          name: '创新指数',
          value: 0,
          strName: 'cxzs',
        },
        {
          name: '成长指数',
          value: 0,
          strName: 'czzs',
        },
        {
          name: '荣誉指数',
          value: 0,
          strName: 'ryzs',
        },
        {
          name: '风险指数',
          value: 0,
          strName: 'fxzz',
        },
      ],
      //企业健康评级左侧数据
      qyjklData: [
        {
          name: '经营活力',
          value: 0,
          strName: 'jyhl',
          keyName: 'jyhlpf',
          max: 24,
        },
        {
          name: '创新驱动',
          value: 0,
          strName: 'cxqd',
          keyName: 'cxqdpf',
          max: 19,
        },
        {
          name: '结构优化',
          value: 0,
          strName: 'jgyh',
          keyName: 'jgyhpf',
          max: 19,
        },
        {
          name: '质效提升',
          value: 0,
          strName: 'zxts',
          keyName: 'zxtspf',
          max: 19,
        },
        {
          name: '风险防范',
          value: 0,
          strName: 'fxff',
          keyName: 'fxffpf',
          max: 19,
        },
      ],
      //企业活跃评级左侧数据
      qyhylData: [
        {
          name: '用电指数',
          value: 89,
          strName: 'ydzs',
        },
        {
          name: '用水指数',
          value: 85,
          strName: 'yszs',
        },
        {
          name: '用气指数',
          value: 80,
          strName: 'yqzs',
        },
        {
          name: '纳税指数',
          value: 78,
          strName: 'nszs',
        },
        {
          name: '用工指数',
          value: 82,
          strName: 'ygzs',
        },
      ],
      //企业基本属性数据
      rightData: [
        {
          name: 0,
          value: [
            {
              name: '注册资本',
              value: '',
              unit: '万元',
              strName: 'zczb',
            },
            {
              name: '员工数量',
              value: '',
              unit: '人',
              strName: 'ygrsxx',
            },
            {
              name: '子公司数',
              value: '',
              unit: '',
              strName: 'zgssxx',
            },
          ],
        },
        {
          name: 1,
          value: [
            {
              name: '专利数量',
              value: '',
              unit: '',
              strName: 'zlslxx',
            },
            {
              name: '研发费用',
              value: '',
              unit: '',
              strName: 'yffyxx',
            },
            {
              name: '研发人员',
              value: '',
              unit: '',
              strName: 'yfryxx',
            },
          ],
        },
        {
          name: 2,
          value: [
            {
              name: '营收增长',
              value: '',
              unit: '万元',
              strName: 'yszzxx',
            },
            {
              name: '资产负债',
              value: '',
              unit: '万元',
              strName: 'zcfzxx',
            },
            {
              name: '人均产值',
              value: '',
              unit: '万元',
              strName: 'rjczxx',
            },
          ],
        },
        {
          name: 3,
          value: [
            {
              name: '百强企业',
              value: '',
              unit: '-',
              strName: 'bqqyxx',
            },
            {
              name: '科技荣誉',
              value: '',
              unit: '-',
              strName: 'kjryxx',
            },
            {
              name: '质量奖励',
              value: '',
              unit: '',
              strName: 'zljlxx',
            },
          ],
        },
        {
          name: 4,
          value: [
            {
              name: '安全生产',
              value: '',
              unit: '',
              strName: 'aqscpf',
            },
            {
              name: '经营风险',
              value: '',
              unit: '',
              strName: 'jyfxpf',
            },
            {
              name: '信用风险',
              value: '',
              unit: '',
              strName: 'xyfxpf',
            },
          ],
        },
      ],
      rightData1: [],
      //企业基本属性右侧数据
      qyzprData: [
        {
          name: 0,
          value: [
            {
              name: '注册资本',
              value: '0',
              unit: '万元',
              strName: 'zczb',
            },
            {
              name: '员工数量',
              value: '0',
              unit: '人',
              strName: 'ygrsxx',
            },
            {
              name: '子公司数',
              value: '0',
              unit: '',
              strName: 'zgssxx',
            },
          ],
        },
        {
          name: 1,
          value: [
            {
              name: '专利数量',
              value: '0',
              unit: '',
              strName: 'zlslxx',
            },
            {
              name: '研发费用',
              value: '0',
              unit: '',
              strName: 'yffyxx',
            },
            {
              name: '研发人员',
              value: '0',
              unit: '',
              strName: 'yfryxx',
            },
          ],
        },
        {
          name: 2,
          value: [
            {
              name: '营收增长',
              value: '0',
              unit: '万元',
              strName: 'yszzxx',
            },
            {
              name: '资产负债',
              value: '0',
              unit: '万元',
              strName: 'zcfzxx',
            },
            {
              name: '人均产值',
              value: '0',
              unit: '万元',
              strName: 'rjczxx',
            },
          ],
        },
        {
          name: 3,
          value: [
            {
              name: '百强企业',
              value: '0',
              unit: '-',
              strName: 'bqqyxx',
            },
            {
              name: '科技荣誉',
              value: '0',
              unit: '-',
              strName: 'kjryxx',
            },
            {
              name: '质量奖励',
              value: '0',
              unit: '',
              strName: 'zljlxx',
            },
          ],
        },
        {
          name: 4,
          value: [
            {
              name: '安全生产',
              value: '0',
              unit: '',
              strName: 'aqscpf',
            },
            {
              name: '经营风险',
              value: '0',
              unit: '',
              strName: 'jyfxpf',
            },
            {
              name: '信用风险',
              value: '0',
              unit: '',
              strName: 'xyfxpf',
            },
          ],
        },
      ],
      //企业健康评级 右侧数据
      qyjkrData: [
        {
          name: 0,
          value: [
            [
              {
                name: '企业净利润增长率',
                value: '-',
                unit: '%',
                strName: 'qyjzczzl',
                keyName: 'qyjlrzzl',
              },
              {
                name: '企业利润总额增长率',
                value: '-',
                unit: '%',
                strName: 'qylrzezzl',
                keyName: 'qylrzezzl',
              },
              {
                name: '企业营业收入增长率',
                value: '-',
                unit: '%',
                strName: 'qyyysrzzl',
                keyName: 'qyyysrzzl',
              },
            ],
            [
              {
                name: '企业纳税额增长率',
                value: '-',
                unit: '%',
                strName: 'qynsezzl',
                keyName: 'qynsezzl',
              },
              {
                name: '企业资产周转率',
                value: '-',
                unit: '%',
                strName: 'zyzczzl',
                keyName: 'zzczzl',
              },
              {
                name: '企业存货周转天数',
                value: '-',
                unit: '天',
                strName: 'qychzzts',
                keyName: 'chzzts',
              },
            ],
            [
              {
                name: '企业应收账款周转天数',
                value: '-',
                unit: '天',
                strName: 'qyyszkzzts',
                keyName: 'yszkzzts',
              },
              // {
              //   name: '企业产值增长率',
              //   value: '33',
              //   unit: '%',
              //   strName: 'qyczzzl',
              // },
            ],
          ],
        },
        {
          name: 1,
          value: [
            [
              {
                name: '高新技术企业(中小型科技型企业)',
                value: '-',
                unit: '',
                strName: 'qyyffyzb',
                keyName: 'gxjsqypf',
              },
              {
                name: '百人专利数',
                value: '-',
                unit: '个',
                strName: 'qyyffyzzl',
                keyName: 'bmryzls',
              },
              {
                name: '百人商标数',
                value: '-',
                unit: '个',
                strName: 'qysfszyfjg',
                keyName: 'bmrysbs',
              },
            ],
          ],
        },
        {
          name: 2,
          value: [
            [
              {
                name: '企业主营业务收入利润率',
                value: '-',
                unit: '%',
                strName: 'qyzyywsrlrl',
                keyName: 'zyywsrlrl',
              },
              {
                name: '是否对外提供担保',
                value: '-',
                unit: '',
                strName: 'qyjzcbzzzl',
                keyName: 'sfdb',
              },
              {
                name: '近期是否存在经营异常',
                value: '-',
                unit: '',
                strName: 'qybkysxlcyryzb',
                keyName: 'jyycpf',
              },
            ],
            [
              {
                name: '企业员工增长率',
                value: '-',
                unit: '%',
                strName: 'qyzjzl',
                keyName: 'ygzzl',
              },
              {
                name: '主要人员为人才占比',
                value: '-',
                unit: '%',
                strName: 'qyrczyzb',
                keyName: 'rczb',
              },
              {
                name: '是否投资其他公司股权',
                value: '-',
                unit: '',
                strName: 'qyxcpcz',
                keyName: 'sfwggq',
              },
            ],
          ],
        },
        {
          name: 3,
          value: [
            [
              {
                name: '是否有新增产品',
                value: '-',
                unit: '',
                strName: 'qyqyldscl',
                keyName: 'xzcplb',
              },
              {
                name: '是否存在招聘',
                value: '-',
                unit: '',
                strName: 'qyjzcsyl',
                keyName: 'zppf',
              },
              {
                name: '是否欠缴社保',
                value: '-',
                unit: '',
                strName: 'qymjzjz',
                keyName: 'qjsb',
              },
            ],
            [
              {
                name: '是否外地购地',
                value: '-',
                unit: '',
                strName: 'qymjss',
                keyName: 'wdgd',
              },
            ],
          ],
        },
        {
          name: 4,
          value: [
            [
              {
                name: '企业信用风险预警评级',
                value: '-',
                unit: '',
                strName: 'qyxyfxyjpj',
                keyName: 'xyfxpf',
                arr: [
                  {
                    num: '1.4',
                    pj: 'B',
                  },
                  {
                    num: '2.8',
                    pj: 'A',
                  },
                ],
              },
              {
                name: '企业安全生产评级',
                value: '-',
                unit: '',
                strName: 'qyaqscpj',
                keyName: 'aqscpf',
                arr: [
                  {
                    num: '2.4',
                    pj: 'A',
                  },
                  {
                    num: '1.2',
                    pj: 'B',
                  },
                ],
              },
              {
                name: '企业劳动保障评级',
                value: '-',
                unit: '',
                strName: 'qyldbzpj',
                keyName: 'ldbzcxdjpf',
                arr: [
                  {
                    num: '3.0',
                    pj: 'A',
                  },
                  {
                    num: '2.4',
                    pj: 'B',
                  },
                  {
                    num: '1.8',
                    pj: 'C',
                  },
                  {
                    num: '1.2',
                    pj: 'D',
                  },
                ],
              },
            ],
            [
              {
                name: '企业资产负债率',
                value: '-',
                unit: '%',
                strName: 'qyzcfzl',
                keyName: 'zcfzl',
              },
              {
                name: '企业环境信用评级',
                value: '-',
                unit: '',
                strName: 'qyhjxypj',
                keyName: 'qyhjxwxypjpf',
                arr: [
                  {
                    num: '3.0',
                    pj: 'A',
                  },
                  {
                    num: '2.4',
                    pj: 'B',
                  },
                  {
                    num: '2.1',
                    pj: 'C',
                  },
                  {
                    num: '1.8',
                    pj: 'D',
                  },
                  {
                    num: '1.5',
                    pj: 'E',
                  },
                  {
                    num: '1.2',
                    pj: 'F',
                  },
                ],
              },
              {
                name: '企业公共信用评级',
                value: '-',
                unit: '',
                strName: 'qyggxypj',
                keyName: 'qyggxypjpf',
                arr: [
                  {
                    num: '3.0',
                    pj: 'A',
                  },
                  {
                    num: '2.4',
                    pj: 'B',
                  },
                  {
                    num: '1.8',
                    pj: 'C',
                  },
                  {
                    num: '1.5',
                    pj: 'D',
                  },
                  {
                    num: '1.2',
                    pj: 'E',
                  },
                ],
              },
            ],
          ],
        },
      ],
      //企业活跃评级 右侧数据
      qyhyrData: [
        {
          name: 0,
          value: [
            [
              {
                name: '近90天申请暂停用电',
                value: '0',
                unit: '次',
                strName: 'qqstsqztyd',
              },
              {
                name: '近90天申请暂停用电累计',
                value: '0',
                unit: '天',
                strName: 'jqstsqztydlj',
              },
              {
                name: '近90天申请暂停单次最长',
                value: '0',
                unit: '天',
                strName: 'jqstsqztdczc',
              },
            ],
            [
              {
                name: '上月用电量环比变化率',
                value: '21',
                unit: '%',
                strName: 'syydlhbbhl',
              },
              {
                name: '上月用电量同比变化率',
                value: '34',
                unit: '%',
                strName: 'syydltbbhl',
              },
              {
                name: '近12个月用电量违约行为',
                value: '21',
                unit: '4分次',
                strName: 'jseyydlwyxw',
              },
            ],
            [
              {
                name: '单位GDP耗电量',
                value: '53',
                unit: '千瓦时/万',
                strName: 'dwgdphdl',
              },
            ],
          ],
        },
        {
          name: 1,
          value: [
            [
              {
                name: '上月用水量环比变化率',
                value: '2',
                unit: '%',
                strName: 'syyslhbbhl',
              },
              {
                name: '上月用水量同比变化率',
                value: '6',
                unit: '%',
                strName: 'syysltbbhl',
              },
              {
                name: '近12个月违约行为',
                value: '0',
                unit: '次',
                strName: 'jsegywyxw',
              },
            ],
            [
              {
                name: '单位GDP耗水量',
                value: '151',
                unit: '立方米/万',
                strName: 'dwgdphsl',
              },
            ],
          ],
        },
        {
          name: 2,
          value: [
            [
              {
                name: '上月用气量环比变化率',
                value: '15',
                unit: '%',
                strName: 'syyqlhbbhl',
              },
              {
                name: '上个月用气量同比变化率',
                value: '10',
                unit: '%',
                strName: 'syyqltbbhl',
              },
              {
                name: '近12个月违约行为',
                value: '4',
                unit: '次',
                strName: 'jsegywyxwq',
              },
            ],
          ],
        },
        {
          name: 3,
          value: [
            [
              {
                name: '开税票环比变化率',
                value: '11',
                unit: '%',
                strName: 'ksphbbhl',
              },
              {
                name: '开税票同比变化率',
                value: '10',
                unit: '%',
                strName: 'ksptbbhl',
              },
              {
                name: '增值税纳税额环比变化率',
                value: '4',
                unit: '%',
                strName: 'zzsnsehbbhl',
              },
            ],
            [
              {
                name: '增值税纳税额环同比变化率',
                value: '15',
                unit: '%',
                strName: 'zzsnsetbbhl',
              },
            ],
          ],
        },
        {
          name: 4,
          value: [
            [
              {
                name: '用工环比变化率',
                value: '5',
                unit: '%',
                strName: 'yghbbhl',
              },
              {
                name: '用工同比变化率',
                value: '15',
                unit: '%',
                strName: 'ygtbbhl',
              },
              {
                name: '上月用工总人数',
                value: '2889',
                unit: '人',
                strName: 'syygzrs',
              },
            ],
          ],
        },
      ],
      //企业外迁风险率-右侧数据
      qywqfxlData: [
        {
          name: 0,
          value: [
            {
              name: '企业用能分析',
              value: '58',
              unit: '分',
              strName: 'qyynfx',
              desc: '上月用电环比变化率|上月用电同比变化率单位GDP耗电量（千瓦时/ 万）|上月用水量环比变化率|上月用水量同比变化率|单位GDP耗水量（立方米 / 万）|上月用气量环比变化率|上月用气量同比变化率',
            },
            {
              name: '企业用工分析',
              value: '91',
              unit: '分',
              strName: 'qyygfx',
              desc: '上月用工环比变化率|上月用工同比变化率|互联网是否有裁员|报道异地招聘人员数',
            },
            {
              name: '企业用地分析',
              value: '86',
              unit: '分',
              strName: 'qyydfx',
              desc: '是否异地购置用地',
            },
          ],
        },
        {
          name: 1,
          value: [
            {
              name: '企业对外投资',
              value: '59',
              unit: '分',
              strName: 'qydwtz',
              desc: '企业异地投资频率（企业、企业法人）|异地投资金额（企业、企业法人）',
            },
            {
              name: '企业享受政策',
              value: '80',
              unit: '分',
              strName: 'qyxszc',
              desc: '是否享受所在地政策|已享受的本地政策数量）',
            },
            {
              name: '企业知识产权',
              value: '85',
              unit: '分',
              strName: 'qyzscq',
              desc: '专利数量环比变化',
            },
          ],
        },
        {
          name: 2,
          value: [
            {
              name: '所在产业链分析',
              value: '76',
              unit: '分',
              strName: 'szcylfx',
              desc: '所在产业链及配套企业数量',
            },
            {
              name: '外地招商情况',
              value: '32',
              unit: '分',
              strName: 'wdzsqk',
              desc: '异地投资接洽报道数量|街道异地官员到访报道数量',
            },
            {
              name: '逆向指标评分',
              value: '50',
              unit: '分',
              strName: 'nxzbpf',
              desc: '四上企业|企业上一年度纳税超1000万企业|从事产业属于区域重点发展产业|上市企业|总部企业|瞪羚企业|独角兽企业|专精特新“小巨人”企业  ',
            },
          ],
        },
      ],
      //企业属性关键字
      keyName: {},
      //企业评级数据
      qypjData: [
        {
          icon: require('@/pages/qyzf/img/qyjkpj.png'),
          name: '企业健康评级',
          value: '-',
          qy: '-',
          hy: '-',
        },
        {
          icon: require('@/pages/qyzf/img/qywqfxl.png'),
          name: '企业外迁风险率',
          value: '-',
        },
        {
          icon: require('@/pages/qyzf/img/jkbgzd.png'),
          name: '健康诊断报告',
          value: '-',
        },
      ],
      currentIndex: 0, //当前切换下标
      qyzpfsz: 0, //企业智评分数
      qyzpfs: 0, //企业智评分数
      currentText: '健康分数',
      isAllfb: false,
      qyyjlx: '',
      qyjkfs: 0, //企业健康分数
      hypjpjHypj: '',
      hyAvg: '',
      rightImgShow: false, //"Error: Cannot find module './undefined.png'"报错修复
    }
  },

  mounted() {
    this.currentIndex = 0
    this.getKeyName()
    // this.rightData1 = this.qywqfxlData
    // this.rightData = this.qywqfxlData

    this.$nextTick(() => {
      // setTimeout(() => {
      this.initFun()
      this.getQypjData('企业健康评级')
      // 确保数据验证后再显示图片
      this.rightImgShow = true
      // }, 3000)
    })
  },
  methods: {
    // 安全获取图片路径
    getImageSrc(strName) {
      try {
        if (!strName || strName === 'undefined' || strName === undefined) {
          console.warn('图片名称无效:', strName, '使用默认图片')
          return require('@/pages/qyzf/img/no-data.png') // 使用默认图片
        }
        return require('@/pages/qyzf/img/xq/' + strName + '.png')
      } catch (error) {
        console.warn('图片加载失败:', strName, error)
        return require('@/pages/qyzf/img/no-data.png') // 返回默认图片
      }
    },
    // 安全获取右侧图片路径
    getRightImageSrc(imgIndex) {
      try {
        if (typeof imgIndex !== 'number' || imgIndex < 0) {
          return require('@/pages/qyzf/img/no-data.png')
        }
        return require('@/pages/qyzf/img/rightImg' + imgIndex + '.png')
      } catch (error) {
        console.warn('右侧图片加载失败:', imgIndex, error)
        return require('@/pages/qyzf/img/no-data.png')
      }
    },
    // 验证并修复数据结构
    validateAndFixData(data) {
      if (!data || !Array.isArray(data)) return data

      return data.map((item) => {
        if (item.value && Array.isArray(item.value)) {
          item.value = item.value.map((subArray) => {
            if (Array.isArray(subArray)) {
              return subArray.map((subItem) => ({
                ...subItem,
                strName: subItem.strName || 'no-data', // 提供默认值
                name: subItem.name || '未知',
                value: subItem.value || '-',
                unit: subItem.unit || '',
              }))
            }
            return subArray
          })
        }
        return item
      })
    },
    // 处理图片加载错误
    handleImageError(event) {
      console.warn('图片加载错误，使用默认图片')
      event.target.src = require('@/pages/qyzf/img/no-data.png')
    },
    getKeyName() {
      let that = this
      getCsdnInterface1('qyhx_ent_comment_al').then((res) => {
        that.keyName = res.data.data[0].json_result
      })
    },
    //轮播下一步操作
    tab_to_next(index) {
      this.$refs.tab[index].next()
    },
    tab_to_prev(index) {
      this.$refs.tab[index].prev()
    },
    //切换改便界面内容与数据
    getQypjData(text) {
      if (text == '企业健康评级') {
        this.currentText = '健康分数'
        this.rightData = this.validateAndFixData(this.qyjkrData)
        this.leftData = this.qyjklData
        this.qyzpfsz = this.qyjkfs
        this.getPageInit(text)
      }
    },
    //接口初始化
    initFun() {
      let that = this
      getCsdnInterface1('qyhx_enterprise_find', { tyshxydm: that.allMessage[0].tyshxydm }).then((res) => {
        let data = res.data.data[0]
        //企业智评左侧仪表盘赋值
        for (let o in that.qyzplData) {
          let name = that.qyzplData[o].strName
          that.qyzplData[o].value = data[name]
        }
        //智评分数赋值
        this.qyzpfs = data['qyzpfs']
        //企业基本属性赋值
        for (let i = 0; i < that.qyzprData.length; i++) {
          for (let j in that.qyzprData[i].value) {
            let rightName = that.qyzprData[i].value[j].strName
            that.qyzprData[i].value[j].value = data[rightName] ? data[rightName] : '-'
          }
        }
        that.leftData = that.qyzplData
        that.rightData = that.qyzprData
        that.qyzpfsz = that.qyzpfs

        let mlhy = (res.data.data[0] && res.data.data[0].mlhyZwmc) || ''
        if (mlhy) {
          getCsdnInterface1('qyhx_avg', { mlhy_zwmc: mlhy }).then((res) => {
            let data = res.data.data[0]
            this.hyAvg = data.qyzfHypj
            this.qypjData[0].hy = data.hynqyzs || '-'
          })
        }
      })

      getCsdnInterface1('qyhx_ent_health_data', { tyshxydm: that.allMessage[0].tyshxydm }).then((res) => {
        let data = res.data.data[0]
        this.qyjkrData.forEach((ele) => {
          let onceArr = ele.value.reduce((last, next) => {
            return last.concat(next)
          }, [])
          onceArr.forEach((item) => {
            if (['xyfxpf', 'aqscpf', 'ldbzcxdjpf', 'qyhjxwxypjpf', 'qyggxypjpf'].includes(item.keyName)) {
              let onceObj = item.arr.find((a) => a.num == Number(data[item.keyName]).toFixed(1))
              item.value = (onceObj && onceObj.pj) || '--'
            } else {
              item.value = /[\u4e00-\u9fa5]/.test(data[item.keyName])
                ? data[item.keyName]
                : (Math.round(data[item.keyName] * 100) / 100).toFixed(1) || '--'
            }
          })
        })

        this.rightData = this.validateAndFixData(this.qyjkrData)
      })

      getCsdnInterface1('qyhx_details', { tyshxydm: that.allMessage[0].tyshxydm }).then((res) => {
        let data = res.data.data
        if (data[0]) {
          this.qyjklData.forEach((ele) => {
            ele.value = Math.round((data[0][ele.keyName] / ele.max) * 100 * 10) / 10 || 0
          })
          this.qypjData[0].qy = data[0].rn || '-'
          this.qypjData[0].value = data[0].qypj.slice(0, 1)
          this.hypjpjHypj = data[0].qypj.slice(0, 1) || '-'
          this.qyzpfsz = this.qyjkfs = data[0].qyzf
          this.getPageInit()
        }
      })

      getCsdnInterface1('qyhx_relocation', { tyshxydm: that.allMessage[0].tyshxydm }).then((res) => {
        let data = res.data.data[0]
        data.qcgl = Number(data.qcgl) < 0 ? '1' : data.qcgl
        this.qypjData[1].value = data.qcgl + '%' || '-'
      })
    },

    //刷新调用echarts数据
    getPageInit(text) {
      let that = this
      for (let i = 0; i < that.leftData.length; i++) {
        let id = 'pie' + i
        that.pieEcharts(id, that.leftData[i], i)
      }
      that.getChart01('qyhx-chart01', this.qyzpfsz, text)
    },
    //绘制半圆仪表盘
    pieEcharts(id, data, colorNum) {
      let myEc = this.$echarts.init(document.getElementById(id))
      var colors = ['#D1545D', '#D27D40', '#E6DA52', '#54D15D']
      var colors1 = ['#54D15D', '#E6DA52', '#D27D40', '#D1545D']
      var colorArr = data.name == '风险指数' ? colors1 : colors

      function getIdx(value) {
        var p = value,
          idx = 0
        if (p > 0 && p <= 60) {
          idx = 0
        } else if (p > 60 && p <= 75) {
          idx = 1
        } else if (p > 75 && p <= 90) {
          idx = 2
        } else if (p > 90 && p <= 100) {
          idx = 3
        }
        return idx
      }

      function getColor(value) {
        return colors[getIdx(value)]
      }
      let option = {
        tooltip: {
          formatter: '{a} : {c}',
        },
        series: [
          {
            name: '智评分数',
            type: 'gauge',
            radius: '130%',
            center: ['50%', '80%'],
            detail: {
              formatter: '{value}',
              fontSize: 32,
              color: data.value > 60 ? colors[3] : data.value > 50 ? colors[1] : data.value ? colors[0] : '',
              offsetCenter: [0, '-30%'],
              fontWeight: 'bold',
            },
            data: [
              {
                value: data.value == '-' ? 0 : data.value,
                name: data.name,
              },
            ],
            min: 0,
            max: 100,
            startAngle: 180,
            endAngle: 0,
            splitNumber: 10,
            title: {
              offsetCenter: [0, 0],
              fontSize: 32,
              color: '#fff',
            },
            pointer: {
              // 仪表盘指针
              show: false,
              length: '50%',
              width: 5,
            },
            itemStyle: {
              // 仪表盘指针样式
              color: getColor(data.value),
              // color: '#1e3a53',
            },
            axisLine: {
              lineStyle: {
                width: 10,
                color: [
                  [0.5, colors[0]],
                  [0.6, colors[1]],
                  // [0.8, colors[2]],
                  [1, colors[3]],
                  // [0.6, colorArr[0]],
                  // [0.75, colorArr[1]],
                  // [0.9, colorArr[2]],
                  // [1, colorArr[3]],
                ],
              },
            },
            axisTick: {
              show: true,
              length: 5,
              lineStyle: {
                width: 1,
              },
            },
            splitLine: {
              show: true,
              length: 10,
              lineStyle: {
                color: '#ffffff80',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 18,
                color: '#ffffff80',
                fontWeight: '',
              },
            },
          },
        ],
      }
      myEc.clear()
      myEc.setOption(option, true)
    },
    //绘制圆环。已废弃
    getChart02(id, data) {
      let myEc = this.$echarts.init(document.getElementById(id))
      let legendData = ['2022年', '2023年']
      let value = data.map((o) => {
        o.value = o.value == '-' ? 0 : o.value
        return o.value
      })
      let option = {
        tooltip: {
          show: true,
          //雷达图的tooltip不会超出div，也可以设置position属性，position定位的tooltip 不会随着鼠标移动而位置变化，不友好
          confine: true,
          enterable: true, //鼠标是否可以移动到tooltip区域内
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          axisPointer: {
            type: 'shadow',
          },
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          show: false,
          orient: 'vertical',
          icon: 'squareRatio', //图例形状
          data: legendData,
          top: '10%',
          right: '5%',
          itemWidth: 14, // 图例标记的图形宽度。[ default: 25 ]
          itemHeight: 14, // 图例标记的图形高度。[ default: 14 ]
          itemGap: 21, // 图例每项之间的间隔。[ default: 10 ]横向布局时为水平间隔，纵向布局时为纵向间隔。
          textStyle: {
            fontSize: 32,
            color: '#fff',
          },
        },
        radar: {
          // shape: 'circle',
          shape: 'polygon',
          center: ['40%', '50%'],
          radius: '80%',
          splitNumber: 5, // 雷达图圈数设置
          name: {
            textStyle: {
              color: '#fff',
              fontSize: 32,
            },
          },
          // 设置雷达图中间射线的颜色
          axisLine: {
            lineStyle: {
              color: '#5F65A9',
            },
          },
          indicator: [
            {
              name: '规模指数',
              max: 100,
            },
            {
              name: '创新指数',
              max: 100,
            },
            {
              name: '成长指数',
              max: 100,
            },
            {
              name: '风险指数',
              max: 100,
            },
            {
              name: '荣誉指数',
              max: 100,
            },
          ],
          name: {
            textStyle: {
              color: '#fff',
              fontSize: 32,
            },
            rich: {
              a: {
                color: '#ffffff',
                fontSize: 30,
                fontFamily: 'SourceHanSansSC-Regular',
                padding: [0, 0, 5, 0],
                // lineHeight: 20
              },
              b: {
                color: '#009bff',
                align: 'center',
                fontSize: 30,
                fontFamily: 'SourceHanSansSC-Regular',
                // padding: 2,
                // borderRadius: 4
              },
            },
            formatter: (a, b) => {
              return a
              // return `{a|${a}}`
            },
          },

          //雷达图背景的颜色，在这儿随便设置了一个颜色，完全不透明度为0，就实现了透明背景
          splitArea: {
            show: false,
            areaStyle: {
              color: 'rgba(255,0,0,0)', // 图表背景的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#5F65A9', // 设置网格的颜色
            },
          },
        },
        series: [
          {
            name: '2022年', // tooltip中的标题
            type: 'radar', //表示是雷达图
            symbol: 'circle', // 拐点的样式，还可以取值'rect','angle'等
            symbolSize: 0, // 拐点的大小
            areaStyle: {
              normal: {
                width: 1,
                opacity: 0.2,
              },
            },
            data: [
              {
                value: value,
                name: '',
                // 设置区域边框和区域的颜色
                itemStyle: {
                  normal: {
                    color: '#52C41A9f',
                    lineStyle: {
                      color: '#52C41A9f',
                    },
                  },
                },
                areaStyle: {
                  normal: {
                    // 单项区域填充样式
                    color: {
                      type: 'linear',
                      x: 0, //右
                      y: 0, //下
                      x2: 1, //左
                      y2: 1, //上
                      colorStops: [
                        {
                          offset: 0,
                          color: '#0AB7FF9f',
                        },
                        {
                          offset: 1,
                          color: '#0AB7FF00',
                        },
                      ],
                      globalCoord: false,
                    },
                    opacity: 1, // 区域透明度
                  },
                },
              },
            ],
          },
        ],
      }

      myEc.setOption(option)
    },
    //绘制整圆仪表盘
    getChart01(id, value, text) {
      let data = Number(value)
      let myEc = echarts.init(document.getElementById(id))
      var colors = ['#D1545D', '#D27D40', '#E6DA52', '#54D15D']
      let max = text == '企业智评' ? 120 : 100

      function getIdx(value) {
        var p = value,
          idx = 0
        if (p > 0 && p <= 40) {
          idx = 0
        } else if (p > 40 && p <= 50) {
          idx = 1
        }
        // else if (p > 75 && p <= 90) {
        //   idx = 2
        // }
        else if (p > 50 && p <= max) {
          idx = 3
        }
        return idx
      }

      function getColor(value) {
        return colors[getIdx(value)]
      }
      let option = {
        tooltip: {
          formatter: '{a} : {c}',
        },
        series: [
          {
            name: '智评分数',
            type: 'gauge',
            radius: '85%',
            center: ['47%', '70%'],
            detail: {
              formatter: '{value}',
              fontSize: 50,
              // color: "#fff",
              offsetCenter: [0, '40%'],
              fontWeight: 'bold',
            },
            data: [
              {
                value: value,
                name: text,
              },
            ],
            min: 0,
            max: max,
            // startAngle: 0,
            // endAngle: 359.9,
            splitNumber: max == 120 ? 8 : 10,
            title: {
              offsetCenter: [0, '50%'],
              fontSize: 32,
              color: '#fff',
              show: true,
            },
            pointer: {
              // 仪表盘指针
              show: true,
              length: '50%',
              width: 10,
            },
            itemStyle: {
              // 仪表盘指针样式
              color: getColor(data),
              // color: getColor(data.value),
              //color: '#1e3a53',
            },
            axisLine: {
              lineStyle: {
                width: 30,
                color:
                  text == '企业智评'
                    ? [
                        [0.5, colors[0]],
                        [0.625, colors[1]],
                        [0.75, colors[2]],
                        [1, colors[3]],
                      ]
                    : [
                        // [0.5, colors[0]],
                        // [0.6, colors[1]],
                        // // [0.8, colors[2]],
                        // [1, colors[3]],
                        [0.4, colors[0]],
                        [0.5, colors[1]],
                        [1, colors[3]],
                      ],
              },
            },
            axisTick: {
              show: true,
              length: 18,
              lineStyle: {
                width: 1,
              },
            },
            splitLine: {
              show: true,
              length: 35,
              lineStyle: {
                color: '#fff',
                fontWeight: '400',
              },
            },
            axisLabel: {
              formatter: function (e) {
                if (max == 120) {
                  switch (e + '') {
                    case '30':
                      return '问题企业'
                    case '75':
                      return '合格'
                    case '90':
                      return '良好'
                    case '105':
                      return '优秀'
                    default:
                      return e
                  }
                } else {
                  // switch (e + '') {
                  //   case '40':
                  //     return '问题\n企业'
                  //   case '60':
                  //     return '亚健康'
                  //   case '70':
                  //     return '健康'
                  //   case '80':
                  //     return '优秀'
                  //   case '100':
                  //     return '标杆'
                  //   default:
                  return e
                  // }
                }
              },
              textStyle: {
                fontSize: 26,
                // color: "#fff",
                fontWeight: '400',
              },
            },
            markLine: {
              lineStyle: {
                color: '#fff',
              },
              data: [
                [
                  {
                    name: max == 120 ? '行业平均值(63)' : `行业平均值\n (${this.hyAvg})`,
                    x: '47%',
                    y: '70%',
                    xAxis: 51,
                    lineStyle: {
                      width: 1.656,
                      color: (this.hyAvg && '#fff') || 'transparent',
                    },
                    label: {
                      show: this.hyAvg != '',
                      fontSize: 32,
                    },
                  },
                  {
                    x: this.hyAvg * 1 + '%', //max == 120 ?  '80%': '60%',
                    y: '15.26065975587649%', //this.hyAvg*0.5+'%'//'15.26065975587649%',
                  },
                ],
              ],
            },
          },
        ],
      }
      myEc.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
/deep/.el-carousel__container {
  height: 200px !important;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
.qyhxCon {
  width: 100%;
  height: 910px;
  margin-bottom: 30px;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
}

.qyhxright-con-item {
  width: 48%;
}

.qypj {
  width: 100%;
  height: 100%;
  padding: 80px 10px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
}

.qypj li {
  width: 36%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
}

/* .qypj li:last-child {
  cursor: auto;
} */
.qypj li img {
  width: 198px;
  height: 231px;
}

.pj-value {
  white-space: nowrap;
  // text-align: center;
  display: flex;
  justify-content: center;
  align-items: baseline;
  font-size: 40px;
  line-height: 62px;
  font-weight: bolder;
  width: 100%;
  margin-top: 20px;
  text-align: center;
  background: linear-gradient(180deg, #d6e7ff 0%, #9bc3ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.jz {
  background: linear-gradient(180deg, #d6e7ff 0%, #fea043 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.qh-con {
  width: 100%;
  height: 2px;
  background-image: url('@/pages/qyzf/img/tab-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
}

.qyhxTab {
  width: 278px;
  height: 30px;
  background-image: url('@/pages/qyzf/img/tab.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  top: -27px;
  left: 90px;
}

.qypj-sm {
  width: 987px;
  height: 21px;
  background-image: url('@/pages/qyzf/img/zpfs-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  top: 120px;
  left: 280px;
}

.sm-zs {
  width: 1000px;
  height: 42px;
  background-image: url('@/pages/qyzf/img/t-zs.png');
  background-repeat: no-repeat;
  position: absolute;
  top: -56px;
  left: -42px;
}

.sm-wz {
  white-space: nowrap;
  line-height: 40px;
  color: #dcefff;
  text-align: center;
  font-size: 32px;
  position: absolute;
  top: -42px;
  left: 111px;
}

.sm-wz span {
  font-size: 36px;
  background: linear-gradient(180deg, #d6e7ff 0%, #9bc3ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bolder;
  margin: 0 10px;
}

.pj-text {
  font-size: 32px;
  color: #dcefff;
  text-align: center;
  margin-top: 20px;
}

::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

::-webkit-scrollbar-thumb {
  border-radius: 6px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}

/* 标题 */
.first-title {
  position: relative;
  width: 100%;
  height: 95px;
  background-image: url('@/pages/qyzf/img/first-title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-left: 100px;
  padding-right: 50px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0;
  line-height: 110px;
  font-size: 45px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
}

.first-title > i,
.two-title > i {
  position: absolute;
  width: 1062px;
  height: 90px;
  display: inline-block;
  background: url('@/pages/qyzf/img/line.png');
  background-size: 100%;
  animation-name: animateLine;
  animation-duration: 10s;
  animation-iteration-count: infinite;
  /* animation-direction:alternate; */
  /* animation-delay */
}

.two-title {
  position: relative;
  width: 100%;
  height: 95px;
  background-image: url('@/pages/qyzf/img/two-title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-left: 100px;
  padding-right: 50px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0;
  line-height: 110px;
  font-size: 45px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
}

.title-text {
  line-height: 110px;
  font-size: 50px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
  position: relative;
}

.flag-click::after {
  content: '';
  width: 26px;
  height: 23px;
  /* background-image: url('/static/images/common/header/click-1.png'); */
  position: absolute;
  top: 50px;
  margin-left: 10px;
}

.title-item {
  font-size: 32px;
  color: rgba(255, 255, 255, 0.843);
  display: flex;
}

.title-item > li {
  margin-right: 50px;
}

.title-item > li:last-child {
  margin-right: 0;
}

.title-item-active {
  color: #fff;
}

/* 智评分数 */
.zbfs-css {
  height: 80px;
  font-size: 32px;
  line-height: 40px;
  color: #fff;
  text-align: center;
  margin: 0 auto;
  background-image: url('@/pages/qyzf/img/zpfs-bg.png');
  background-size: 250px 35px;
  background-repeat: no-repeat;
  background-position: center 17px;
}

/* 底部 */
.con-bottom {
  height: 1080px;
  display: flex;
  justify-content: space-between;
}

.con-left {
  width: 414px;
  height: 100%;
}

.con-left > div {
  margin-bottom: 15px;
}

.con-right {
  width: 1500px;
  height: 100%;
  /* background-color: teal; */
}

.blue-bg {
  background: #0ab7ff10;
}

.qyhx-ul-list {
  width: 100%;
  height: 200px;
  margin-bottom: 20px;
  font-size: 32px;
  color: #dcefff;
  display: flex;
  justify-content: space-around;
  padding-left: 80px;
  box-sizing: border-box;
  position: relative;
}

.qyhx-ul-list:last-child {
  margin-bottom: 0;
}

.qyhx-li-list {
  width: 30%;
  height: 100%;
  display: flex;
  align-items: center;
}

.qyhx-img {
  width: 158px;
  height: 160px;
}

.qyhx-img > img {
  width: 100%;
  height: 100%;
}

.qyhx-li-content {
  flex: 1;
  padding-left: 0px;
  box-sizing: border-box;
  text-align: center;
}

.qyhx-line {
  width: 240px;
  height: 26px;
  border-color: #dcefff;
  background-image: url('@/pages/qyzf/img/line2.png');
  background-size: 100% 100%;
  margin-bottom: 10px;
}

.color-size {
  font-size: 36px !important;
}

.value-css {
  font-size: 42px;
  background: linear-gradient(180deg, #d6e7ff 0%, #9bc3ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.icon-box {
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-css {
  width: 120px;
  height: 120px;
}

.icon-css > img {
  width: 100%;
  height: 100%;
}

.el-carousel__item {
  display: flex;
}

.el-carousel__arrow {
  font-size: 40px;
  color: #3bbff8;
}

.el-icon-arrow-left:before {
  content: '\e792';
}

.el-icon-arrow-right:before {
  content: '\e791';
}

.el-carousel__indicator--horizontal {
  display: none;
}

.el-carousel__arrow--left {
  display: none;
}

.right-a {
  position: absolute;
  right: -30px;
  z-index: 9;
  cursor: pointer;
  width: 145px;
  height: 145px;
  top: 30px;
}

.left-a {
  transform: rotate(175deg);
  position: absolute;
  left: -30px;
  z-index: 9;
  cursor: pointer;
  width: 145px;
  height: 145px;
  top: 20px;
}

.color-yel {
  background: linear-gradient(180deg, #ff4f1d 0%, #ffa41d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.el-message__content,
.el-message__icon {
  font-size: 28px;
}

.el-message--error {
  background-color: #062643;
  border: none;
}
</style>
<style lang="less">
.el-carousel__indicators {
  display: none !important;
}
.el-popover {
  background: #062845;
  color: #fff !important;
  font-size: 22px !important;
  border: none !important;
  font-size: 30px;
  .el-popover__title {
    font-size: 30px !important;
    color: #fff;
  }
}
.el-popper[x-placement^='bottom'] .popper__arrow {
  border-bottom-color: #1b5ad7;
}

.el-popper[x-placement^='bottom'] .popper__arrow::after {
  border-bottom-color: #132c4ed0;
}
</style>