<template>
  <div class="fxyjfxLeft">
    <div class="left1">
      <MainTitle title="重点区域预警" />
      <SubTitle title="24小时人流变化情况" />
      <div id="rlbh_hour" class="rlbh_hour"></div>
      <SubTitle title="近30天人流变化情况" />
      <div id="rlbh_day" class="rlbh_day"></div>
      <SubTitle title="重点区域预警" />
      <div id="zdqy_yj" class="zdqy_yj"></div>
    </div>
    <div class="left2">
      <MainTitle title="网格精细预警" />
      <SubTitle title="24小时人流变化情况" />
      <div id="rlbh_hour1" class="rlbh_hour1"></div>
      <SubTitle title="近30天人流变化情况" />
      <div id="rlbh_day1" class="rlbh_day1"></div>
      <SubTitle title="网格预警" />
      <div id="wg_yj" class="wg_yj"></div>
    </div>
  </div>
</template>

<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import MainTitle from '@/components/MainTitle.vue'
import SubTitle from '@/components/SubTitle.vue'
export default {
  name: 'fxyjfxLeft',
  components: { MainTitle, SubTitle },
  data() {
    return {}
  },

  mounted() {
    this.$nextTick(() => {
      this.initApi()
    })
  },

  methods: {
    initApi() {
      
    },
  },
}
</script>

<style scoped lang="less">
* {
  margin: 0;
  padding: 0;
}

.fxyjfxLeft {
  width: 2262px;
  display: flex;
  .left1 {
    width: 48%;
    .rlbh_hour {
      width: 100%;
      height: 464px;
    }
    .rlbh_day {
      width: 100%;
      height: 464px;
    }
    .zdqy_yj {
      width: 100%;
      height: 610px;
    }
  }
  .left2 {
    width: 48%;
    margin-left: 79px;
    .rlbh_hour1 {
      width: 100%;
      height: 464px;
    }
    .rlbh_day1 {
      width: 100%;
      height: 464px;
    }
    .wg_yj {
      width: 100%;
      height: 610px;
    }
  }
}
</style>