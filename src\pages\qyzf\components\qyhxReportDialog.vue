<template>
  <el-dialog
    :top="winTop"
    ref="commomDialog"
    custom-class="custom-class-dialog"
    :show-close="false"
    :modal="false"
    width="2200px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="visible"
    :modal-append-to-body="true"
    :append-to-body="true"
  >
    <div class="container">
      <i class="el-icon-close" style="float: right; zoom: 5; cursor: pointer" @click="close()"></i>
      <h3 style="font-size: 50px; text-align: center; line-height: 250px" v-show="noData">暂无企业数据</h3>
      <div v-if="info" class="info_box" v-loading="loading">
        <!-- 第一页 -->
        <div class="part_box">
          <div class="name">{{ info.qymc }}</div>
          <div class="type">健康诊断报告</div>
          <div class="date">{{ info.currentDateV }}</div>
        </div>
        <!-- 第二页 -->
        <div class="part_box1">
          <div class="title">健康诊断结果总览</div>
          <div class="line"></div>
          <div class="content_box">
            <div class="content">
              <span class="txt1">{{ info.qymc }}</span>
              <span class="txt2">健康诊断评级总分为：</span>
              <span class="txt1">{{ info.qyzf }}分</span>
              <span class="txt2">，评级结果为：</span>
              <span class="txt1">{{ info.qypj }}</span>
            </div>
            <div class="content mgt40">
              <span class="txt1">{{ info.qymc }}</span>
              <span class="txt2">处于</span>
              <span class="txt1">{{ info.mlhyZwmc }}</span>
              <span class="txt2">，该行业健康诊断评级平均分为</span>
              <span class="txt3">{{ info.qyzfHypj }}分</span>
              <span class="txt2">,</span>
              <span class="txt1">{{ info.qymc }}</span>
              <span class="txt2">在行业中排名情况：</span>
              <span class="txt3">{{ info.rn }}/{{ info.hynqyzs }}</span>
            </div>
          </div>
          <div id="radar1_chart" class="radar1_chart" ref="radar1_chart"></div>
        </div>
        <!-- 第三页 -->
        <div class="part_box2">
          <div class="title">健康诊断结果总览</div>
          <div class="line"></div>
          <div class="content_box">
            <div class="name_box">
              <div class="name">1、经营活力分析</div>
              <div class="tips">（经营活力评价满分为24）</div>
            </div>
            <div class="content">
              该企业经营活力诊断评分为{{ info.jyhlpf }},行业诊断平均分为{{ info.jyhlpfHypj }}，{{
                Number(info.jyhlpf) > Number(info.jyhlpfHypj) ? '高于' : '低于'
              }}行业平均水平。
            </div>
          </div>
          <div class="chart_box">
            <div class="legend_box">
              <div class="legend_item">
                <div class="icon"></div>
                <div class="name">差项(&lt;评价标准60%)</div>
              </div>
              <div class="legend_item">
                <div class="icon color1"></div>
                <div class="name">强项(≥评价标准60%)</div>
              </div>
            </div>
            <div class="tips_txt">各分析项评分</div>
            <div id="bar1_chart" class="bar_chart" ref="bar1_chart"></div>
          </div>
          <div class="static_box">
            <div class="static_title">评价项详细数值</div>
            <div class="static_list">
              <div class="static_item">
                <div>企业利润总额增长率</div>
                <div>{{ info.healthData.qylrzezzl }}%</div>
              </div>
              <div class="static_item">
                <div>企业净利润增长率</div>
                <div>{{ info.healthData.qyjlrzzl }}%</div>
              </div>
              <div class="static_item">
                <div>企业资产周转率</div>
                <div>{{ info.healthData.zzczzl }}%</div>
              </div>
              <div class="static_item">
                <div>企业存货周转天数</div>
                <div>{{ info.healthData.chzzts }}天</div>
              </div>
              <div class="static_item">
                <div>企业应收账款周转天数</div>
                <div>{{ info.healthData.yszkzzts }}天</div>
              </div>
              <div class="static_item">
                <div>企业营业收入增长率</div>
                <div>{{ info.healthData.qyyysrzzl }}%</div>
              </div>
              <div class="static_item">
                <div>企业纳税额增长率</div>
                <div>{{ info.healthData.qynsezzl }}%</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 第四页 -->
        <div class="part_box2">
          <div class="title">健康诊断结果总览</div>
          <div class="line"></div>
          <div class="content_box">
            <div class="name_box">
              <div class="name">2、创新驱动分析</div>
              <div class="tips">（创新驱动评价满分为19）</div>
            </div>
            <div class="content">
              该企业创新驱动诊断评分为{{ info.cxqdpf }},行业诊断平均分为{{ info.cxqdpfHypj }}，{{
                Number(info.cxqdpf) > Number(info.cxqdpfHypj) ? '高于' : '低于'
              }}行业平均水平。
            </div>
          </div>
          <div class="chart_box">
            <div class="legend_box">
              <div class="legend_item">
                <div class="icon"></div>
                <div class="name">差项(&lt;评价标准60%)</div>
              </div>
              <div class="legend_item">
                <div class="icon color1"></div>
                <div class="name">强项(≥评价标准60%)</div>
              </div>
            </div>
            <div class="tips_txt">各分析项评分</div>
            <div id="bar2_chart" class="bar_chart" ref="bar2_chart"></div>
          </div>
          <div class="static_box">
            <div class="static_title">评价项详细数值</div>
            <div class="static_list">
              <div class="static_item">
                <div>是否为高新技术企业</div>
                <div>{{ info.healthData.gxjsqypf }}</div>
              </div>
              <div class="static_item">
                <div>百人商标数</div>
                <div>{{ info.healthData.bmrysbs }}个</div>
              </div>
              <div class="static_item">
                <div>百人专利数</div>
                <div>{{ info.healthData.bmryzls }}个</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 第五页 -->
        <div class="part_box2">
          <div class="title">健康诊断结果总览</div>
          <div class="line"></div>
          <div class="content_box">
            <div class="name_box">
              <div class="name">3、结构优化分析</div>
              <div class="tips">（结构优化评价满分为19）</div>
            </div>
            <div class="content">
              该企业结构优化诊断评分为{{ info.jgyhpf }},行业诊断平均分为{{ info.jgyhpfHypj }}，{{
                Number(info.jgyhpf) > Number(info.jgyhpfHypj) ? '高于' : '低于'
              }}行业平均水平。
            </div>
          </div>
          <div class="chart_box">
            <div class="legend_box">
              <div class="legend_item">
                <div class="icon"></div>
                <div class="name">差项(&lt;评价标准60%)</div>
              </div>
              <div class="legend_item">
                <div class="icon color1"></div>
                <div class="name">强项(≥评价标准60%)</div>
              </div>
            </div>
            <div class="tips_txt">各分析项评分</div>
            <div id="bar3_chart" class="bar_chart" ref="bar3_chart"></div>
          </div>
          <div class="static_box">
            <div class="static_title">评价项详细数值</div>
            <div class="static_list">
              <div class="static_item">
                <div>企业主营业务收入利润率</div>
                <div>{{ info.healthData.zyywsrlrl }}%</div>
              </div>
              <div class="static_item">
                <div>企业人才占比</div>
                <div>{{ info.healthData.rczb }}%</div>
              </div>
              <div class="static_item">
                <div>是否存在经营异常</div>
                <div>{{ info.healthData.jyycpf }}</div>
              </div>
              <div class="static_item">
                <div>员工增长率</div>
                <div>{{ info.healthData.ygzzl }}%</div>
              </div>
              <div class="static_item">
                <div>对外担保</div>
                <div>{{ info.healthData.sfdb }}</div>
              </div>
              <div class="static_item">
                <div>投资其他公司</div>
                <div>{{ info.healthData.sfwggq }}</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 第六页 -->
        <div class="part_box2">
          <div class="title">健康诊断结果总览</div>
          <div class="line"></div>
          <div class="content_box">
            <div class="name_box">
              <div class="name">4、质效提升分析</div>
              <div class="tips">（质效提升评价满分为19）</div>
            </div>
            <div class="content">
              该企业质效提升诊断评分为{{ info.zxtspf }},行业诊断平均分为{{ info.zxtspfHypj }}，{{
                Number(info.zxtspf) > Number(info.zxtspfHypj) ? '高于' : '低于'
              }}行业平均水平。
            </div>
          </div>
          <div class="chart_box">
            <div class="legend_box">
              <div class="legend_item">
                <div class="icon"></div>
                <div class="name">差项(&lt;评价标准60%)</div>
              </div>
              <div class="legend_item">
                <div class="icon color1"></div>
                <div class="name">强项(≥评价标准60%)</div>
              </div>
            </div>
            <div class="tips_txt">各分析项评分</div>
            <div id="bar4_chart" class="bar_chart" ref="bar4_chart"></div>
          </div>
          <div class="static_box">
            <div class="static_title">评价项详细数值</div>
            <div class="static_list">
              <div class="static_item">
                <div>是否存在新增产品类别</div>
                <div>{{ info.healthData.xzcplb }}</div>
              </div>
              <div class="static_item">
                <div>是否欠缴社保</div>
                <div>{{ info.healthData.qjsb }}</div>
              </div>
              <div class="static_item">
                <div>是否存在招聘</div>
                <div>{{ info.healthData.zppf }}</div>
              </div>
              <div class="static_item">
                <div>是否外地购地</div>
                <div>{{ info.healthData.wdgd }}</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 第七页 -->
        <div class="part_box2">
          <div class="title">健康诊断结果总览</div>
          <div class="line"></div>
          <div class="content_box">
            <div class="name_box">
              <div class="name">5、风险防范分析</div>
              <div class="tips">（风险防范评价满分为19）</div>
            </div>
            <div class="content">
              该企业风险防范诊断评分为{{ info.fxffpf }},行业诊断平均分为{{ info.fxffpfHypj }}，{{
                Number(info.fxffpf) > Number(info.fxffpfHypj) ? '高于' : '低于'
              }}行业平均水平。
            </div>
          </div>
          <div class="chart_box">
            <div class="legend_box">
              <div class="legend_item">
                <div class="icon"></div>
                <div class="name">差项(&lt;评价标准60%)</div>
              </div>
              <div class="legend_item">
                <div class="icon color1"></div>
                <div class="name">强项(≥评价标准60%)</div>
              </div>
            </div>
            <div class="tips_txt">各分析项评分</div>
            <div id="bar5_chart" class="bar_chart" ref="bar5_chart"></div>
          </div>
          <div class="static_box">
            <div class="static_title">评价项详细数值</div>
            <div class="static_list">
              <div class="static_item">
                <div>企业资产负债率</div>
                <div>{{ info.healthData.zcfzl }}%</div>
              </div>
              <div class="static_item">
                <div>企业信用风险评级</div>
                <div>{{ info.healthData.xyfxpf }}</div>
              </div>
              <div class="static_item">
                <div>企业环境信用评级</div>
                <div>{{ info.healthData.qyhjxwxypjpf }}</div>
              </div>
              <div class="static_item">
                <div>企业安全生产评级</div>
                <div>{{ info.healthData.aqscpf }}</div>
              </div>
              <div class="static_item">
                <div>企业劳动保障评级</div>
                <div>{{ info.healthData.ldbzcxdjpf }}</div>
              </div>
              <div class="static_item">
                <div>企业公共信用评级</div>
                <div>{{ info.healthData.qyggxypjpf }}</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 第八页 -->
        <div class="part_box2 min_box_heihgt">
          <div class="title">企业信息展示</div>
          <div class="line"></div>
          <div class="info_title mgt80">1、企业基本信息展示</div>
          <div class="info_box">
            <div class="info_item">
              <div class="info_item_name">法定代表人</div>
              <div class="info_item_txt">{{ info.fddbr }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">统一信用代码</div>
              <div class="info_item_txt">{{ info.tyshxydm }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">所属地区</div>
              <div class="info_item_txt">{{ info.ssdq }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">状态</div>
              <div class="info_item_txt">{{ info.zt }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">注册资本</div>
              <div class="info_item_txt">{{ info.zczb }}{{ info.zczbBz }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">成立日期</div>
              <div class="info_item_txt">{{ info.clrq }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">工商注册号</div>
              <div class="info_item_txt">{{ info.gszch }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">主体身份代码</div>
              <div class="info_item_txt">{{ info.ztsfdm }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">企业登记机关</div>
              <div class="info_item_txt">{{ info.qydjjg }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">企业类型</div>
              <div class="info_item_txt">{{ info.qylx }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">经营范围</div>
              <div class="info_item_txt">{{ info.jyfw }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">营业期限</div>
              <div class="info_item_txt">{{ info.yyqx }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">生产经营地行政区划</div>
              <div class="info_item_txt">{{ info.scjydszxzqh }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">生产经营地</div>
              <div class="info_item_txt">{{ info.scjyd }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">门类行业代码</div>
              <div class="info_item_txt">{{ info.mlhydm }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">门类行业-中文名称</div>
              <div class="info_item_txt">{{ info.mlhyZwmc }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">经营场所</div>
              <div class="info_item_txt">{{ info.jycs }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">住所所在地</div>
              <div class="info_item_txt">{{ info.zsszd }}</div>
            </div>
            <div class="info_item">
              <div class="info_item_name">公司联系电话</div>
              <div class="info_item_txt max_width">{{ info.gslxdh }}</div>
            </div>
          </div>
          <div class="info_title mgt50">2、企业经营信息</div>
          <div class="info_box2" v-if="info.cwXx && info.cwXx.length > 0">
            <div class="info_item2">
              <div class="info_item_name">2022年纳税金额</div>
              <div class="info_item_txt">{{ info.cwXx[0].nsze }}万元</div>
            </div>
            <div class="info_item2">
              <div class="info_item_name">2022年企业销售总额</div>
              <div class="info_item_txt">{{ info.cwXx[0].xs_yysr }}万元</div>
            </div>
            <div class="info_item2">
              <div class="info_item_name">2022年利润总额</div>
              <div class="info_item_txt">{{ info.cwXx[0].lrze }}万元</div>
            </div>
            <div class="info_item2">
              <div class="info_item_name">2022年资产总计</div>
              <div class="info_item_txt">{{ info.cwXx[0].zcze }}万元</div>
            </div>
            <div class="info_item2">
              <div class="info_item_name">2022年负债总计</div>
              <div class="info_item_txt">{{ info.cwXx[0].fzze }}万元</div>
            </div>
          </div>
        </div>
        <!-- 第九页 -->
        <div class="part_box2" v-if="info.qygmXx">
          <div class="title">企业信息展示</div>
          <div class="line"></div>
          <div class="info_title mgt80">3、企业纳税曲线</div>
          <div class="line_chart1" id="line_chart1" ref="line_chart1"></div>
          <div class="info_title mgt160">4、企业社保缴纳曲线</div>
          <div class="total">员工总数:{{ info.qygmXx[0].yljfrs }}</div>
          <div class="line_chart2" id="line_chart2" ref="line_chart2"></div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import $ from 'jquery'
import * as echarts from 'echarts'
import { gePdf } from '@/api/csdnIndexApi'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    tyshxydm: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      winTop: '40px',
      noData: false,
      // tyshxydm: '91330701MA8G4H8B5N',
      loading: false,
      info: null,
    }
  },
  watch: {
    if(newVal) {
      let that = this
      this.$nextTick(() => {
        setTimeout(() => {
          that.winTop =
            (document.documentElement.clientHeight - $(that.$refs.commomDialog.$el).find('.el-dialog').height()) / 2 +
            'px'
        }, 100)
      })
    },
  },
  mounted() {
    let that = this
    that.$nextTick(() => {
      that.init()
    })
  },
  methods: {
    close() {
      this.$emit('close')
    },
    init() {
      this.getData()
    },
    //雷达图
    getChart1() {
      const el = this.$refs.radar1_chart
      if (!el) {
        // DOM 尚未渲染，直接返回，等待下一次调用
        return
      }
      let myEc = echarts.init(el)
      let data1 = [
        Number(this.info.cxqdpf),
        Number(this.info.jgyhpf),
        Number(this.info.jyhlpf),
        Number(this.info.zxtspf),
        Number(this.info.fxffpf),
      ]
      let data2 = [
        Number(this.info.cxqdpfHypj),
        Number(this.info.jgyhpfHypj),
        Number(this.info.jyhlpfHypj),
        Number(this.info.zxtspfHypj),
        Number(this.info.fxffpfHypj),
      ]
      let option = {
        tooltip: {
          //雷达图的tooltip不会超出div，也可以设置position属性，position定位的tooltip 不会随着鼠标移动而位置变化，不友好
          confine: true,
          enterable: true, //鼠标是否可以移动到tooltip区域内
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          valueFormatter: function (value) {
            return value + '分'
          },
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        radar: {
          name: {
            textStyle: {
              color: '#222222',
              fontSize: 28,
            },
          },
          shape: 'polygon',
          center: ['50%', '50%'],
          radius: '60%',
          // startAngle: 120,
          scale: true,
          axisLine: {
            lineStyle: {
              color: '#C9CDD4',
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#F2F3F5', // 设置网格的颜色
            },
          },
          indicator: [
            {
              name: '创新驱动',
              max: 19,
            },
            {
              name: '结构优化',
              max: 19,
            },
            {
              name: '经营活力',
              max: 24,
            },
            {
              name: '质效提升',
              max: 19,
            },
            {
              name: '风险防范',
              max: 19,
            },
          ],
          splitArea: {
            show: true,
            areaStyle: {
              color: ['#E5E6EB', '#fff'],
              opacity: 0.4,
            },
          },
        },
        grid: {
          position: 'center',
        },
        polar: {
          center: ['50%', '50%'], // 默认全局居中
          radius: '0%',
        },
        angleAxis: {
          min: 0,
          interval: 5,
          clockwise: false,
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
        radiusAxis: {
          min: 0,
          interval: 20,
          splitLine: {
            show: false,
          },
        },
        legend: {
          orient: 'vertical',
          bottom: 25,
          left: '40%',
          icon: 'circle',
          itemWidth: 12, // 图例标记的图形宽度。[ default: 25 ]
          itemHeight: 12, // 图例标记的图形高度。[ default: 14 ]
          itemGap: 20, // 图例每项之间的间隔。[ default: 10 ]横向布局时为水平间隔，纵向布局时为纵向间隔。
          textStyle: {
            fontSize: 28,
            color: '#222',
          },
          data: ['企业健康诊断评分', '行业健康诊断评分'],
        },
        series: [
          {
            name: '企业健康诊断评分',
            type: 'radar',
            symbol: 'circle', // 拐点的样式，还可以取值'rect','angle'等
            symbolSize: 10, // 拐点的大小
            itemStyle: {
              normal: {
                color: '#1058BE',
              },
            },
            areaStyle: {
              normal: {
                color: '#1058BE',
                opacity: 0.1,
              },
            },
            lineStyle: {
              width: 2,
              color: '#1058BE',
            },
            label: {
              normal: {
                show: true,
                formatter: (params) => {
                  return params.value + '分'
                },
                color: '#000',
              },
            },
            data: [
              {
                value: data1,
              },
            ],
          },
          {
            name: '行业健康诊断评分',
            type: 'radar',
            symbol: 'circle', // 拐点的样式，还可以取值'rect','angle'等
            symbolSize: 10, // 拐点的大小
            itemStyle: {
              normal: {
                color: '#D16213',
              },
            },
            areaStyle: {
              normal: {
                color: '#D16213',
                opacity: 0.1,
              },
            },
            lineStyle: {
              width: 2,
              color: '#D16213',
            },
            label: {
              normal: {
                show: true,
                formatter: (params) => {
                  return params.value + '分'
                },
                color: '#000',
              },
            },
            data: [
              {
                value: data2,
              },
            ],
          },
        ],
      }
      myEc.setOption(option)
    },
    //柱状图1-经营活力分析
    getChart21() {
      const el = this.$refs.bar1_chart
      if (!el) return
      let myEc = echarts.init(el)
      let that = this
      let option = {
        tooltip: {
          trigger: 'item',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          enterable: true, //鼠标是否可以移动到tooltip区域内
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          valueFormatter: function (value) {
            return value + '分'
          },
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        grid: {
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: [
            '企业净利润增长率',
            '企业资产周转率',
            '企业存货周转天数',
            '企业应收账款周转天数',
            '企业营业收入增长率',
            '企业利润总额增长率',
            '企业纳税额增长率',
          ],
          axisLine: {
            lineStyle: {
              color: 'rgba(60, 97, 137, .4)',
            },
            show: true, //不显示坐标轴线
          },
          axisLabel: {
            textStyle: {
              color: '#666666',
              fontSize: 24,
            },
            interval: 0,
            rotate: 70,
          },
        },
        yAxis: {
          name: '',
          type: 'value',
          nameTextStyle: {
            fontSize: 24,
            color: '#666666',
            padding: 5,
          },
          splitLine: {
            lineStyle: {
              color: '#AAB8C6',
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 24,
              color: '#666666',
            },
          },
        },
        series: [
          {
            name: '',
            type: 'bar',
            barWidth: 48,
            data: [
              {
                name: '企业净利润增长率',
                value: that.info.qyjlrzzlpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.qyjlrzzlpf) < 2.1
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '企业资产周转率',
                value: that.info.qyzczzlpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.qyzczzlpf) < 2.1
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '企业存货周转天数',
                value: that.info.qychzztspf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.qychzztspf) < 1.8
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '企业应收账款周转天数',
                value: that.info.qyyszkzztspf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.qyyszkzztspf) < 2.1
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '企业营业收入增长率',
                value: that.info.qyyysrzzlpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.qyyysrzzlpf) < 2.1
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '企业利润总额增长率',
                value: that.info.qylrzezzlpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.qylrzezzlpf) < 2.1
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '企业纳税额增长率',
                value: that.info.qynsezzlpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.qynsezzlpf) < 2.1
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
            ],
          },
        ],
      }

      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },
    //柱状图2-创新驱动分析
    getChart22() {
      const el = this.$refs.bar2_chart
      if (!el) return
      let myEc = echarts.init(el)
      let that = this
      let option = {
        tooltip: {
          trigger: 'item',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          enterable: true, //鼠标是否可以移动到tooltip区域内
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          valueFormatter: function (value) {
            return value + '分'
          },
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        grid: {
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: ['是否为高新技术企业', '百人商标数', '百人专利数'],
          axisLine: {
            lineStyle: {
              color: 'rgba(60, 97, 137, .4)',
            },
            show: true, //不显示坐标轴线
          },
          axisLabel: {
            textStyle: {
              color: '#666666',
              fontSize: 24,
            },
            interval: 0,
            rotate: 70,
          },
        },
        yAxis: {
          name: '',
          type: 'value',
          nameTextStyle: {
            fontSize: 24,
            color: '#666666',
            padding: 5,
          },
          splitLine: {
            lineStyle: {
              color: '#AAB8C6',
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 24,
              color: '#666666',
            },
          },
        },
        series: [
          {
            name: '',
            type: 'bar',
            barWidth: 48,
            data: [
              {
                name: '是否为高新技术企业',
                value: that.info.gxjsqypf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.gxjsqypf) < 3.9
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '百人商标数',
                value: that.info.bmrysbspf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.bmrysbspf) < 3.6
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '百人专利数',
                value: that.info.bmryzlspf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.bmryzlspf) < 3.9
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
            ],
          },
        ],
      }

      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },
    //柱状图3-结构优化分析
    getChart23() {
      const el = this.$refs.bar3_chart
      if (!el) return
      let myEc = echarts.init(el)
      let that = this
      let option = {
        tooltip: {
          trigger: 'item',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          enterable: true, //鼠标是否可以移动到tooltip区域内
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          valueFormatter: function (value) {
            return value + '分'
          },
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        grid: {
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: [
            '企业主营业务收入利润率',
            '企业人才占比',
            '是否存在经营异常',
            '员工增长率',
            '对外担保',
            '投资其他公司',
          ],
          axisLine: {
            lineStyle: {
              color: 'rgba(60, 97, 137, .4)',
            },
            show: true, //不显示坐标轴线
          },
          axisLabel: {
            textStyle: {
              color: '#666666',
              fontSize: 24,
            },
            interval: 0,
            rotate: 70,
          },
        },
        yAxis: {
          name: '',
          type: 'value',
          nameTextStyle: {
            fontSize: 24,
            color: '#666666',
            padding: 5,
          },
          splitLine: {
            lineStyle: {
              color: '#AAB8C6',
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 24,
              color: '#666666',
            },
          },
        },
        series: [
          {
            name: '',
            type: 'bar',
            barWidth: 48,
            data: [
              {
                name: '企业主营业务收入利润率',
                value: that.info.zyywsrpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.zyywsrpf) < 2.4
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '企业人才占比',
                value: that.info.rczbpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.rczbpf) < 1.8
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '是否存在经营异常',
                value: that.info.qyjyycpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.qyjyycpf) < 1.8
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '员工增长率',
                value: that.info.qyygzzpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.qyygzzpf) < 1.8
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '对外担保',
                value: that.info.dbpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.dbpf) < 1.8
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '投资其他公司',
                value: that.info.wggqpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.wggqpf) < 1.8
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
            ],
          },
        ],
      }

      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },
    //柱状图4-质效提升分析
    getChart24() {
      const el = this.$refs.bar4_chart
      if (!el) return
      let myEc = echarts.init(el)
      let that = this
      let option = {
        tooltip: {
          trigger: 'item',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          enterable: true, //鼠标是否可以移动到tooltip区域内
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          valueFormatter: function (value) {
            return value + '分'
          },
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        grid: {
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: ['是否存在新增产品类别', '是否欠缴社保', '是否存在招聘', '是否外地购地'],
          axisLine: {
            lineStyle: {
              color: 'rgba(60, 97, 137, .4)',
            },
            show: true, //不显示坐标轴线
          },
          axisLabel: {
            textStyle: {
              color: '#666666',
              fontSize: 24,
            },
            interval: 0,
            rotate: 70,
          },
        },
        yAxis: {
          name: '',
          type: 'value',
          nameTextStyle: {
            fontSize: 24,
            color: '#666666',
            padding: 5,
          },
          splitLine: {
            lineStyle: {
              color: '#AAB8C6',
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 24,
              color: '#666666',
            },
          },
        },
        series: [
          {
            name: '',
            type: 'bar',
            barWidth: 48,
            data: [
              {
                name: '是否存在新增产品类别',
                value: that.info.xzcplbpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.xzcplbpf) < 3
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '是否欠缴社保',
                value: that.info.qjsbpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.qjsbpf) < 2.4
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '是否存在招聘',
                value: that.info.zppf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.zppf) < 3
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '是否外地购地',
                value: that.info.wdgdpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.wdgdpf) < 3
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
            ],
          },
        ],
      }

      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },
    //柱状图5-风险防范分析
    getChart25() {
      const el = this.$refs.bar5_chart
      if (!el) return
      let myEc = echarts.init(el)
      let that = this
      let option = {
        tooltip: {
          trigger: 'item',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          enterable: true, //鼠标是否可以移动到tooltip区域内
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          valueFormatter: function (value) {
            return value + '分'
          },
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        grid: {
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: [
            '企业资产负债率',
            '企业信用风险评级',
            '企业环境信用评级',
            '企业安全生产评级',
            '企业劳动保障评级',
            '企业公共信用评级',
          ],
          axisLine: {
            lineStyle: {
              color: 'rgba(60, 97, 137, .4)',
            },
            show: true, //不显示坐标轴线
          },
          axisLabel: {
            textStyle: {
              color: '#666666',
              fontSize: 24,
            },
            interval: 0,
            rotate: 70,
          },
        },
        yAxis: {
          name: '',
          type: 'value',
          nameTextStyle: {
            fontSize: 24,
            color: '#666666',
            padding: 5,
          },
          splitLine: {
            lineStyle: {
              color: '#AAB8C6',
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 24,
              color: '#666666',
            },
          },
        },
        series: [
          {
            name: '',
            type: 'bar',
            barWidth: 48,
            data: [
              {
                name: '企业资产负债率',
                value: that.info.qyzcfzlpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.qyzcfzlpf) < 2.1
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '企业信用风险评级',
                value: that.info.xyfxpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.xyfxpf) < 2.1
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '企业环境信用评级',
                value: that.info.qyhjxwxypjpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.qyhjxwxypjpf) < 1.8
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '企业安全生产评级',
                value: that.info.aqscpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.aqscpf) < 1.8
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '企业劳动保障评级',
                value: that.info.ldbzcxdjpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.ldbzcxdjpf) < 1.8
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
              {
                name: '企业公共信用评级',
                value: that.info.qyggxypjpf,
                itemStyle: {
                  normal: {
                    color:
                      Number(that.info.qyggxypjpf) < 1.8
                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(16, 88, 190, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(16, 88, 190, 0.32)',
                            },
                          ])
                        : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                              offset: 0,
                              color: 'rgba(209, 98, 19, 1)',
                            },
                            {
                              offset: 1,
                              color: 'rgba(209, 98, 19, 0.32)',
                            },
                          ]),
                  },
                },
              },
            ],
          },
        ],
      }

      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },
    //折线图1
    getChart4() {
      const el = this.$refs.line_chart1
      if (!el) return
      let myChart = echarts.init(el)
      let resdata = this.info.cwXx
      let xdata = [],
        ydata = []
      resdata.forEach((item) => {
        xdata.unshift(item.nbnd)
        ydata.unshift(item.nsze)
      })
      let option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          valueFormatter: function (value) {
            return value + '万元'
          },
          textStyle: {
            color: 'white',
            fontSize: '24',
          },
        },
        legend: {
          orient: 'horizontal',
          // icon: "circle",
          right: '5%',
          itemGap: 45,
          textStyle: {
            color: '#2C3542',
            fontSize: 24,
          },
        },
        grid: {
          left: '5%',
          right: '6%',
          top: '20%',
          bottom: '1%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: xdata,
            axisLine: {
              lineStyle: {
                color: 'rgba(65, 97, 128, 0.45)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: true,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: '#666666',
                fontSize: 24,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位:万元',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: 'rgba(0, 0, 0, 0.85)',
              padding: 15,
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(65, 97, 128, 0.15)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 24,
                color: '#666',
              },
            },
          },
        ],
        series: [
          {
            name: '企业纳税总额',
            type: 'line', // 直线ss
            smooth: true,
            symbolSize: 12,
            symbol: 'circle', //将小圆点改成实心 不写symbol默认空心
            itemStyle: {
              normal: {
                color: '#1058BE',
              },
            },
            data: ydata,
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
    //折线图2
    getChart5() {
      const el = this.$refs.line_chart2
      if (!el) return
      let myChart = echarts.init(el)
      let resdata = this.info.qygmXx
      if (resdata.length > 12) {
        resdata = resdata.slice(0, 12)
      }
      let xdata = [],
        ydata = []
      resdata.forEach((item) => {
        xdata.unshift(item.sjqb)
        ydata.unshift(item.yljfrs)
      })
      let option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          valueFormatter: function (value) {
            return value + '人'
          },
          textStyle: {
            color: 'white',
            fontSize: '24',
          },
        },
        legend: {
          orient: 'horizontal',
          // icon: "circle",
          right: '5%',
          itemGap: 45,
          textStyle: {
            color: '#2C3542',
            fontSize: 24,
          },
        },
        grid: {
          left: '5%',
          right: '6%',
          top: '20%',
          bottom: '1%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: xdata,
            axisLine: {
              lineStyle: {
                color: 'rgba(65, 97, 128, 0.45)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: true,
            },
            axisLabel: {
              interval: 0,
              rotate: 40,
              textStyle: {
                color: '#666666',
                fontSize: 24,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位:人数',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: 'rgba(0, 0, 0, 0.85)',
              padding: 15,
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(65, 97, 128, 0.15)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 24,
                color: '#666',
              },
            },
          },
        ],
        series: [
          {
            name: '社保缴纳情况',
            type: 'line', // 直线ss
            smooth: false,
            symbolSize: 12,
            symbol: 'circle', //将小圆点改成实心 不写symbol默认空心
            itemStyle: {
              normal: {
                color: '#1058BE',
              },
            },
            data: ydata,
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
    getData() {
      let that = this
      that.loading = true
      gePdf({
        tyshxydm: this.tyshxydm,
      }).then((res) => {
        if (res.data.code == 500) {
          that.loading = false
          that.noData = true
          return
        }
        let resdata = res.data.data
        resdata.healthData.qylrzezzl = Number(resdata.healthData.qylrzezzl).toFixed(1)
        resdata.healthData.qyjlrzzl = Number(resdata.healthData.qyjlrzzl).toFixed(1)
        resdata.healthData.zzczzl = Number(resdata.healthData.zzczzl).toFixed(1)
        resdata.healthData.chzzts = Number(resdata.healthData.chzzts).toFixed(1)
        resdata.healthData.yszkzzts = Number(resdata.healthData.yszkzzts).toFixed(1)
        resdata.healthData.qyyysrzzl = Number(resdata.healthData.qyyysrzzl).toFixed(1)
        resdata.healthData.qynsezzl = Number(resdata.healthData.qynsezzl).toFixed(1)
        resdata.healthData.zyywsrlrl = Number(resdata.healthData.zyywsrlrl).toFixed(1)
        resdata.healthData.rczb = Number(resdata.healthData.rczb).toFixed(1)
        resdata.healthData.ygzzl = Number(resdata.healthData.ygzzl).toFixed(1)
        resdata.healthData.zcfzl = Number(resdata.healthData.zcfzl).toFixed(1)
        that.info = resdata
        that.loading = false
        that.noData = false
        that.$nextTick(() => {
          that.getChart1()
          that.getChart21()
          that.getChart22()
          that.getChart23()
          that.getChart24()
          that.getChart25()
          that.getChart4()
          that.getChart5()
        })
      })
    },
  },
}
</script>

<style lang="less" scoped>
.container {
  width: 2050px;
  margin: 0 auto;
  background: #f4f4f4;
}

.info_box {
  overflow-y: auto;
  height: 1800px;
}

.part_box {
  width: 1190px;
  height: 1864px;
  background: #ffffff;
  margin: 0 auto;
  margin-bottom: 48px;
  text-align: center;
}

.part_box .name {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 700;
  font-size: 64px;
  color: #000000;
  line-height: 93px;
  padding-top: 374px;
}

.part_box .type {
  font-family: Source Han Serif CN, Source Han Serif CN;
  font-weight: 400;
  font-size: 56px;
  color: #000000;
  line-height: 80px;
  margin-top: 135px;
}

.part_box .date {
  font-family: Source Han Serif CN, Source Han Serif CN;
  font-weight: 400;
  font-size: 48px;
  color: #000000;
  line-height: 69px;
  margin-top: 798px;
}

.part_box1 {
  width: 1190px;
  height: 1864px;
  background: #ffffff;
  margin: 0 auto;
  margin-bottom: 48px;
  text-align: center;
  padding: 0 100px;
  box-sizing: border-box;
}

.part_box1 .title {
  font-family: Source Han Serif CN, Source Han Serif CN;
  font-weight: 700;
  font-size: 56px;
  color: #000000;
  line-height: 80px;
  padding-top: 98px;
}

.part_box1 .line {
  width: 978px;
  height: 4px;
  background: #000000;
  margin: 58px auto 0;
}

.part_box1 .content_box .content {
  text-align: left;
  margin-top: 80px;
}

.part_box1 .content_box .content .txt1 {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 32px;
  color: #1058be;
  line-height: 46px;
}

.part_box1 .content_box .content .txt2 {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 32px;
  color: #000;
  line-height: 46px;
}

.part_box1 .content_box .content .txt3 {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 32px;
  color: #d16213;
  line-height: 46px;
}

.mgt40 {
  margin-top: 40px;
}

.part_box2 {
  width: 1190px;
  height: 1864px;
  background: #ffffff;
  margin: 0 auto;
  margin-bottom: 48px;
  text-align: center;
  padding: 0 100px;
  box-sizing: border-box;
}

.min_box_heihgt {
  height: auto !important;
  min-height: 1864px !important;
  padding-bottom: 64px;
}

.part_box2 .title {
  font-family: Source Han Serif CN, Source Han Serif CN;
  font-weight: 700;
  font-size: 56px;
  color: #000000;
  line-height: 80px;
  padding-top: 98px;
}

.part_box2 .line {
  width: 978px;
  height: 4px;
  background: #000000;
  margin: 58px auto 0;
}

.part_box2 .content_box .name_box {
  display: flex;
  margin-top: 80px;
  align-items: baseline;
  align-content: baseline;
}

.part_box2 .content_box .name_box .name {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 48px;
  color: #000000;
}

.part_box2 .content_box .name_box .tips {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 32px;
  color: #000000;
}

.part_box2 .content_box .content {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 32px;
  color: #000000;
  line-height: 46px;
  margin-top: 16px;
  text-align: left;
  text-indent: 64px;
}

.radar1_chart {
  width: 990px;
  height: 600px;
  background: #ffffff;
  margin: 64px auto 0;
}

.bar_chart {
  height: 640px;
  width: 900px;
  background: #ffffff;
  margin: 64px auto 0;
}

.chart_box {
  margin-top: 48px;
  position: relative;
}

.chart_box .legend_box {
  display: flex;
  justify-content: flex-end;
}

.chart_box .legend_box .legend_item {
  display: flex;
  align-items: center;
  align-content: center;
  margin-left: 90px;
}

.chart_box .legend_box .legend_item .icon {
  width: 16px;
  height: 16px;
  background: #1058be;
}

.chart_box .legend_box .legend_item .icon.color1 {
  background: #d16213;
}

.chart_box .legend_box .legend_item .name {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 24px;
  color: #2c3542;
  line-height: 24px;
  margin-left: 18px;
}

.chart_box .tips_txt {
  width: 24px;
  height: 375px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 24px;
  color: rgba(44, 53, 66, 0.65);
  position: absolute;
  top: 160px;
  left: 0;
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
}

.static_box {
  margin-top: 48px;
}

.static_box .static_title {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 32px;
  color: #000000;
  line-height: 46px;
}

.static_box .static_list {
  margin-top: 32px;
}

.static_box .static_list .static_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-content: center;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 28px;
  color: #333333;
  line-height: 56px;
}

.part_box2 .info_title {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 48px;
  color: #000000;
  line-height: 70px;
  text-align: left;
}

.part_box2 .info_box {
  margin-top: 24px;
  display: flex;
  flex-wrap: wrap;
  border-top: 1px solid #c7d3de;
  border-left: 1px solid #c7d3de;
  box-sizing: border-box;
}

.part_box2 .info_box .info_item {
  display: flex;
  box-sizing: border-box;
}

.part_box2 .info_box .info_item .info_item_name {
  width: 190px;
  padding: 9px 0 9px 14px;
  background: rgba(66, 143, 252, 0.1);
  box-sizing: border-box;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 18px;
  color: #333333;
  line-height: 24px;
  text-align: left;
  border-right: 1px solid #c7d3de;
  border-bottom: 1px solid #c7d3de;
  display: flex;
  align-items: center;
  align-content: center;
}

.part_box2 .info_box .info_item .info_item_txt {
  width: 302px;
  padding: 9px 0 9px 14px;
  background: fff;
  box-sizing: border-box;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 18px;
  color: #333333;
  line-height: 24px;
  text-align: left;
  border-right: 1px solid #c7d3de;
  border-bottom: 1px solid #c7d3de;
  display: flex;
  align-items: center;
  align-content: center;
}

.part_box2 .info_box .info_item .max_width {
  width: 798px;
}

.info_box2 {
  margin-top: 30px;
  border: 1px solid #c7d3de;
  border-bottom: 0;
}

.info_box2 .info_item2 {
  display: flex;
  border-bottom: 1px solid #c7d3de;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 24px;
  color: #333333;
}

.info_box2 .info_item2 .info_item_name {
  border-right: 1px solid #c7d3de;
  width: 338px;
  background: rgba(66, 143, 252, 0.1);
  padding: 30px 0 30px 14px;
  text-align: left;
  box-sizing: border-box;
}

.info_box2 .info_item2 .info_item_txt {
  width: 648px;
  background: #fff;
  padding: 30px 0 30px 14px;
  text-align: left;
  box-sizing: border-box;
}

.part_box2 .total {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 32px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 32px;
  text-align: left;
  margin-top: 42px;
  margin-left: 32px;
}

.line_chart1 {
  width: 980px;
  height: 400px;
  margin: 30px auto 0;
}

.line_chart2 {
  width: 980px;
  height: 400px;
  margin: 30px auto 0;
}

.mgt80 {
  margin-top: 80px;
}

.mgt50 {
  margin-top: 50px;
}

.mgt160 {
  margin-top: 160px;
}
</style>
