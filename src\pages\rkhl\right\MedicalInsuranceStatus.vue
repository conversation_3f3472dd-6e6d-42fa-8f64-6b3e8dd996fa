<template>
  <div class="chart-item">
    <SubTitle title="医疗保险情况" />
    <div class="chart-container">
      <div id="medicalInsuranceStatus" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'

export default {
  name: 'MedicalInsuranceStatus',
  components: { SubTitle },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      const chart = this.$echarts.init(document.getElementById('medicalInsuranceStatus'))
      const option = {
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          textStyle: { color: '#fff', fontSize: 14 },
        },
        legend: {
          bottom: 0,
          textStyle: { color: '#fff' },
        },
        series: [
          {
            type: 'pie',
            radius: ['35%', '60%'],
            center: ['50%', '45%'],
            label: { color: '#fff' },
            color: ['#91cc75', '#fac858', '#ee6666'],
            data: [
              { value: 520, name: '城镇职工' },
              { value: 380, name: '城乡居民' },
              { value: 100, name: '未参保' },
            ],
          },
        ],
      }
      chart.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>

