const loginTab = [
  {
    path: '/login',
    name: '登录',
    component: () => import('@/pages/login/index'),
    meta: {
      title: '登录',
    },
  },
]
const tabRoutes = [
  {
    path: '/qyzf',
    name: '工业智服',
    component: () => import('@/pages/qyzf/index'),
    meta: {
      title: '工业智服',
    },
  },
  {
    path: '/dyCyl',
    name: '产业链',
    component: () => import('@/pages/dyCyl/index'),
    meta: {
      title: '产业链',
    },
  },
  {
    path: '/qyzfCyl',
    name: '产业链',
    component: () => import('@/pages/qyzfCyl/index'),
    meta: {
      title: '产业链',
    },
  },
  {
    path: '/rkhl',
    name: '人口全域感知',
    component: () => import('@/pages/rkhl/index'),
    meta: {
      title: '人口全域感知',
    },
  },
  {
    path: '/fxyjfx',
    name: '风险预警分析',
    component: () => import('@/pages/fxyjfx/index'),
    meta: {
      title: '风险预警分析',
    },
  },
]
const options = {
  routes: [
    {
      path: '/',
      name: '登录',
      redirect: '/login',
      component: () => import('@/layouts/mainLayoutLogin'),
      children: loginTab,
      meta: {
        title: '登录',
      },
    },
    {
      path: '/qyzf',
      name: '首页',
      redirect: '/qyzf',
      component: () => import('@/layouts/mainLayout'),
      children: tabRoutes,
      meta: {
        title: '首页',
      },
    },
  ],
}
export { tabRoutes, options }
