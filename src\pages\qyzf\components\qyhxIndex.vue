<template>
  <el-dialog
    :top="winTop"
    ref="commomDialog"
    custom-class="custom-class-dialog"
    :show-close="false"
    :modal="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="visible"
    :modal-append-to-body="true"
    :append-to-body="true"
  >
    <div class="qyhxContainer">
      <div class="inneLeft">
        <qyhxLeft :allMessage="allMessage" :qyhxGsbq="qyhxGsbq" :qyhxJbxxList="qyhxJbxxList"></qyhxLeft>
      </div>
      <div class="inneCenter">
        <qyhxCenter :allMessage="allMessage" @qyhxGsbq="handleQyhxGsbq" @qyhxJbxxList="handleQyhxJbxxList"></qyhxCenter>
      </div>
      <div class="innerRight">
        <qyhxRight :allMessage="allMessage"></qyhxRight>
      </div>
      <!-- 左右的刻度背景 -->
      <div class="left-time"></div>
      <div class="right-time"></div>
      <div class="back-btn" @click="close"></div>
    </div>
  </el-dialog>
</template>
<script>
import $ from 'jquery'
import axios from 'axios'
import { getCsdnInterface2 } from '@/api/csdnIndexApi'
import qyhxLeft from '@/pages/qyzf/components/qyhxLeft.vue'
import qyhxRight from '@/pages/qyzf/components/qyhxRight.vue'
import qyhxCenter from '@/pages/qyzf/components/qyhxCenter.vue'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    allMessage: {
      type: Array,
      default: () => [
        {
          fddbr: '黄新建',
          hylx: '软件和信息技术服务业',
          qymc: '数字金华技术运营有限公司',
          tyshxydm: '913101160530238043',
        },
      ],
    },
  },
  components: { qyhxLeft, qyhxRight, qyhxCenter },
  data() {
    return {
      winTop: '40px',
      qyhxGsbq: '',
      qyhxJbxxList: '',
    }
  },
  watch: {
    if(newVal) {
      let that = this
      this.$nextTick(() => {
        setTimeout(() => {
          that.winTop =
            (document.documentElement.clientHeight - $(that.$refs.commomDialog.$el).find('.el-dialog').height()) / 2 +
            'px'
        }, 100)
      })
    },
  },
  mounted() {
    this.$EventBus.$emit('changeTopTitle', this.allMessage[0].qymc)
  },
  methods: {
    handleQyhxGsbq(value) {
      this.qyhxGsbq = value
    },
    handleQyhxJbxxList(value) {
      this.qyhxJbxxList = value
    },
    close() {
      this.$emit('close')
      this.$EventBus.$emit('changeTopTitle', '工业智服')
    },
  },
}
</script>

<style lang="less" scoped>
// 重置element-ui弹框
/deep/.el-dialog {
  width: 7680px;
  height: 1920px;
  // background: rgba(0, 15, 55, 0.9);
  background: transparent;
  background-image: url('@/pages/qyzf/img/qyhxBg.jpg');
  background-size: 100% 100%;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  position: absolute;
  left: 0;
  top: 0;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}

.qyhxContainer {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
  // background: url('~@/assets/application/dtBg.png') no-repeat center center;
  // background-size: 100% 100%;
  .inneLeft {
    position: absolute;
    top: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .innerRight {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .inneCenter {
    position: absolute;
    left: 2135px;
    top: 0;
    z-index: 2;
  }
}
.left-time {
  width: 70px;
  height: 1950px;
  background-image: url('@/pages/qyzf/img/left-time.png');
  background-size: 100% 100%;
  position: absolute;
  left: 2050px;
}

.right-time {
  width: 70px;
  height: 1950px;
  background-image: url('@/pages/qyzf/img/right-time.png');
  background-size: 100% 100%;
  position: absolute;
  left: 5562px;
}

.back-btn {
  width: 120px;
  height: 120px;
  position: absolute;
  top: 210px;
  left: 5450px;
  background-image: url('@/pages/qyzf/img/qyhxCenter/back.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  z-index: 910;
  cursor: pointer;
}
</style>
