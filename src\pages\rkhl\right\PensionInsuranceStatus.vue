<template>
  <div class="chart-item">
    <SubTitle title="养老保险情况" />
    <div class="chart-container">
      <div id="pensionInsuranceStatus" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'

export default {
  name: 'PensionInsuranceStatus',
  components: { SubTitle },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      const chart = this.$echarts.init(document.getElementById('pensionInsuranceStatus'))
      const option = {
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          textStyle: { color: '#fff', fontSize: 14 },
        },
        series: [
          {
            type: 'pie',
            radius: ['35%', '60%'],
            center: ['50%', '50%'],
            label: { color: '#fff' },
            color: ['#73c0de', '#5470c6'],
            data: [
              { value: 820, name: '参保' },
              { value: 180, name: '未参保' },
            ],
          },
        ],
      }
      chart.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>

