<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-04-14 11:38:22
 * @LastEditors: wjb
 * @LastEditTime: 2025-06-11 15:25:44
-->
<template>
  <div class="qyzf-container">
    <div class="inneLeft">
      <wrapbox bg="left" :width="2886">
        <rkhlLeft class="animate__animated animate__fadeInLeft" />
      </wrapbox>
    </div>
    <div class="innerRight">
      <wrapbox bg="right" :width="2351">
        <rkhlRight class="animate__animated animate__fadeInRight" />
      </wrapbox>
    </div>
  </div>
</template>

<script>
import wrapbox from '@/components/wrapbox'
import rkhlLeft from './components/rkhlLeft.vue'
import rkhlRight from './components/rkhlRight.vue'

export default {
  name: 'RkhlIndex',
  data() {
    return {}
  },
  components: {
    wrapbox,
    rkhlLeft,
    rkhlRight
  },
  mounted() {},
  methods: {}
}
</script>

<style scoped lang="less">
.qyzf-container {
  position: relative;
  width: 100%;
  height: 100%;

  .inneLeft {
    position: absolute;
    top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }

  .innerRight {
    position: absolute;
    top: 229px;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
}
</style>
