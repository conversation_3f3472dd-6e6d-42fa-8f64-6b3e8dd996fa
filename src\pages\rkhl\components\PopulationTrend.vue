<template>
  <div class="chart-item">
    <SubTitle title="人口变化趋势" />
    <div class="chart-container">
      <div class="chart-controls">
        <span
          class="control-btn"
          :class="{ active: currentType === '月' }"
          @click="changeEchartHour('月')"
        >
          近三年
        </span>
        <span
          class="control-btn"
          :class="{ active: currentType === '天' }"
          @click="changeEchartHour('天')"
        >
          近一月
        </span>
        <span
          class="control-btn"
          :class="{ active: currentType === '时' }"
          @click="changeEchartHour('时')"
        >
          24小时
        </span>
      </div>
      <div class="time_picker" v-if="currentType === '时'">
        <el-date-picker
          style="width: 260px"
          class="time_box"
          v-model="timeData"
          type="date"
          placeholder="选择日期时间"
          value-format="yyyy-MM-dd"
          @change="timeChangeFun"
        />
      </div>
      <div id="populationTrend" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import moment from 'moment'

export default {
  name: 'PopulationTrend',
  components: {
    SubTitle,
  },
  data() {
    return {
      currentType: '月', // 当前选中的时间类型：月、天、时
      timeData: moment(new Date()).format('YYYY-MM-DD'), // 24小时模式下的日期选择
      cityName: { name: '金华市', code: 330700 }, // 默认城市信息
      chart: null,
    }
  },
  mounted() {
    this.initPopulationTrend()
    this.changeEchartHour('月') // 默认加载近三年数据
  },
  methods: {
    // 人口变化趋势
    initPopulationTrend() {
      if (this.chart) {
        this.chart.dispose()
      }
      this.chart = this.$echarts.init(document.getElementById('populationTrend'))
    },

    // 根据类型切换图表数据
    async changeEchartHour(type) {
      const trunFun = {
        月: async () => {
          try {
            const a1 = await getCsdnInterface1('csrk_snrybhqs', { addressCode: this.cityName.code })

            const xdata = []
            const datas = []

            if (a1.data && a1.data.data && a1.data.data.length > 0) {
              const lastObj = a1.data.data[a1.data.data.length - 1]
              const fisObj = (Number(lastObj.time.slice(0, 4)) - 3).toString() + lastObj.time.slice(4)
              const stertIndex = a1.data.data.findIndex((a) => a.time === fisObj)

              a1.data.data.slice(stertIndex).forEach((ele) => {
                xdata.push(ele.time)
                datas.push((ele.average_count / 10000).toFixed(2))
              })
            }

            this.getLine01(xdata, datas, '万人')
          } catch (error) {
            console.error('获取近三年数据失败:', error)
            this.getLine01([], [], '万人')
          }
        },
        天: async () => {
          try {
            const res = await getCsdnInterface1('csrk_ssrkldrk30', { addressCode: this.cityName.code })
            if (res.data && res.data.data) {
              const xdata = res.data.data.map((a) => a.insert_time.slice(5, 10))
              const ssrk = res.data.data.map((a) => a.max_count)
              this.getLine01(xdata, ssrk, '人')
            }
          } catch (error) {
            console.error('获取近一月数据失败:', error)
            this.getLine01([], [], '人')
          }
        },
        时: () => {
          this.timeChangeFun(this.timeData)
        },
      }

      if (trunFun[type]) {
        await trunFun[type]()
        this.currentType = type
      }
    },

    // 24小时数据获取
    async timeChangeFun(selectedDate) {
      if (!selectedDate) return

      try {
        const res = await getCsdnInterface1('csrk_ssrkldrk24', {
          addressCode: this.cityName.code,
          time: selectedDate
        })

        if (res.data && res.data.data) {
          const xdata = []
          const ssrk = []
          const allData = []

          res.data.data.forEach((ele, i) => {
            if (i === 0 || ele.insert_time.slice(11, 13) !== res.data.data[i - 1].insert_time.slice(11, 13)) {
              allData.push(ele)
            }
          })

          allData.forEach((a) => {
            xdata.push(a.insert_time.slice(11, 13) + '点')
            ssrk.push(a.population_count)
          })

          this.getLine01(xdata, ssrk, '人')
        }
      } catch (error) {
        console.error('获取24小时数据失败:', error)
        this.getLine01([], [], '人')
      }
    },

    // 绘制折线图
    getLine01(datax, datas, unit) {
      if (!this.chart) {
        this.initPopulationTrend()
      }

      // 颜色配置
      const colors = ['#ffc460']

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          textStyle: { color: '#fff', fontSize: 14 },
          formatter: function(params) {
            let result = params[0].name + '<br/>'
            result += `<span style="color:${params[0].color}">实时人口: ${params[0].value}${unit}</span><br/>`
            return result
          }
        },
        grid: {
          left: '10%',
          right: '10%',
          top: '20%',
          bottom: '20%',
        },
        xAxis: {
          type: 'category',
          data: datax,
          axisLabel: { color: '#fff', fontSize: 12 },
          axisLine: { lineStyle: { color: '#333' } },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 12,
            formatter: `{value}${unit}`
          },
          axisLine: { lineStyle: { color: '#333' } },
          splitLine: { lineStyle: { color: '#333' } },
        },
        series: [
          {
            name: '实时人口',
            type: 'line',
            data: datas,
            smooth: false,
            lineStyle: { color: colors[0], width: 2 },
            itemStyle: { color: colors[0] },
            symbol: 'none',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(255, 196, 96, 0.6)' // 顶部颜色，透明度0.6
                  },
                  {
                    offset: 1,
                    color: 'rgba(255, 196, 96, 0.1)' // 底部颜色，透明度0.1
                  }
                ]
              }
            },
          },
        ],
      }

      this.chart.setOption(option, true)
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart-controls {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      gap: 8px;
      z-index: 10;

      .control-btn {
        width: 120px;
        height: 40px;
        font-size: 24px;
        font-weight: 400;
        color: #bfbcbc;
        background: rgba(0, 74, 166, 0.3);
        border: 2px solid #215293;
        text-align: center;
        line-height: 40px;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          color: #ffffff;
          font-weight: 700;
          background: rgba(0, 74, 166, 0.8);
        }

        &:hover {
          background: rgba(0, 74, 166, 0.6);
          color: #ffffff;
        }
      }
    }

    .chart {
      width: 100%;
      height: 470px;
    }

    .time_picker {
      position: absolute;
      top: 60px;
      right: 10px;
      z-index: 20;

      .time_box {
        background: rgba(0, 74, 166, 0.3);
        border: 2px solid #215293;
        border-radius: 4px;

        /deep/ .el-input__inner {
          background: transparent;
          border: none;
          color: #ffffff;
          font-size: 14px;

          &::placeholder {
            color: #bfbcbc;
          }
        }

        /deep/ .el-input__suffix {
          .el-input__suffix-inner {
            .el-input__icon {
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}
</style>
