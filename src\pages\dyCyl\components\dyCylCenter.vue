<template>
  <div class="container">
    <div class="con newh">
      <!-- 企业总览 -->
      <div class="con-item">
        <MainTitle title="企业总览" />
        <div class="item-contents cyzyClass hs">
          <div class="leftTopList">
            <div class="leftTopItem">
              <div class="left" style="background: url('/static/citybrain/qyzf/img/cyl/qyzlItemBg1.png')">
                <img src="/static/citybrain/qyzf/img/cyl/qyzlItem1.png" />
              </div>
              <div class="right" style="background: url('/static/citybrain/qyzf/img/cyl/blue_bg.png')">
                <div class="name">企业总数</div>
                <div class="num" v-if="cylId == 102 || cylId == 212">
                  {{ companyTotal }}
                  <span class="unit">家</span>
                </div>
                <div class="num" v-else-if="cylId == 104">
                  {{ xnyqcVal }}
                  <span class="unit">家</span>
                </div>
                <div class="num" v-else>
                  {{ gsTotal }}
                  <span class="unit">家</span>
                </div>
                <div class="rate">
                  环比增长
                  <div class="value">1%↑</div>
                </div>
              </div>
            </div>
            <div class="leftTopItem" style="margin-left: 45px">
              <div class="left" style="background: url('/static/citybrain/qyzf/img/cyl/qyzlItemBg2.png')">
                <img src="/static/citybrain/qyzf/img/cyl/qyzlItem2.png" />
              </div>
              <div class="right" style="background: url('/static/citybrain/qyzf/img/cyl/green_bg.png')">
                <div class="name">规上企业总数</div>
                <div class="num">
                  {{ gsTotal }}
                  <span class="unit">家</span>
                </div>
                <div class="rate">
                  环比增长
                  <div class="value">3%</div>
                </div>
              </div>
            </div>
          </div>

          <SubTitle
            title="重点培育企业"
            :show-icon="true"
            @icon-click="showPyqyDialog"
          />
          <div id="pieChart1" style="width: 100%; height: 385px"></div>
        </div>
      </div>

      <!-- 重点项目 -->
      <div class="con-item">
        <MainTitle title="重点项目">
          <template #extra>
            <div class="newflex">
              <div
                class="tab_item"
                v-for="(item, index) in tabList"
                @click="tabClick(index)"
                :class="tabIndex == index ? 'tab_active' : ''"
              >
                <span>{{ item }}</span>
              </div>
            </div>
            <div class="dotClass" @click="showDialog"></div>
          </template>
        </MainTitle>
        <div class="item-contents ht">
          <div class="upTwoClass" style="height: 100px">
            <div class="upsClass">
              <div class="txtS">重点项目</div>
              <div class="countCard">
                <div class="number" v-for="(item, i) in xms" :key="i">
                  <div class="numbg" v-if="item != ',' && item != '.'">
                    <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                  </div>
                  <div v-else>{{ item }}</div>
                </div>
              </div>
              <div class="txtS">个</div>
            </div>
          </div>
          <div class="xm_box">
            <div class="xm_item">
              <div class="xm_item_left">
                <img src="@/assets/img/dyCyl/xm_icon1.png" alt="" />
              </div>
              <div class="xm_item_right">
                <div class="xm_txt">{{ tabIndex == 0 ? '2024' : '2025' }}计划投资</div>
                <div class="xm_img">
                  <img src="@/assets/img/dyCyl/xm_icon_line.png" alt="" />
                </div>
                <div class="xm_txt1">
                  {{ ztz }}
                  <span class="unitNew">亿元</span>
                </div>
              </div>
            </div>
            <div class="xm_item">
              <div class="xm_item_left">
                <img src="@/assets/img/dyCyl/xm_icon2.png" alt="" />
              </div>
              <div class="xm_item_right">
                <div class="xm_txt">{{ tabIndex == 0 ? '2024' : '2025' }}完成投资</div>
                <div class="xm_img">
                  <img src="@/assets/img/dyCyl/xm_icon_line.png" alt="" />
                </div>
                <div class="xm_txt1">
                  {{ zs }}
                  <span class="unitNew">亿元</span>
                </div>
              </div>
            </div>
            <div class="newChartWrap">
              <div style="width: 100%; height: 100%" id="hjfb-chartNew"></div>
            </div>
          </div>

          <SubTitle
            title="重点项目清单"
            :show-icon="true"
            @icon-click="showXmqdDialog"
          />
          <div id="pieChart2" style="width: 100%; height: 385px"></div>
        </div>
      </div>
    </div>

    <div class="con newh">
      <!-- 资源要素 -->
      <div class="con-item">
        <MainTitle title="资源要素" />
        <div class="item-contents cyzyClass">
          <div class="leftBottomList">
            <div class="leftBottomItem" v-for="(item, i) in leftBottom" :key="i">
              <div class="name">{{ item.name }}</div>
              <div class="num">
                {{ item.num }}
                <span class="unit">{{ item.unit }}</span>
              </div>
            </div>
          </div>

          <div class="chartZzt">
            <div class="chartZztClass" id="chartZztId"></div>
          </div>
        </div>
      </div>

      <!-- 产业政策 -->
      <div class="con-item">
        <MainTitle title="产业政策" />

        <div class="item-contents cyzyClass">
          <div class="upTwoClassTwo" style="height: 100px; margin: 50px 0">
            <div class="upsClassTwo">
              <div class="txtS">政策总数</div>
              <div class="countCard">
                <div class="number" v-for="(item, i) in String(cyzc.zczs)" :key="i">
                  <div class="numbg" v-if="item != ',' && item != '.' && item != '-'">
                    <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                  </div>
                  <div v-else>{{ item }}</div>
                </div>
              </div>
              <div class="txtS">个</div>
            </div>
            <div class="upsClassTwo">
              <div class="txtS">政策兑付金额</div>
              <div class="countCard">
                <div class="number" v-for="(item, i) in String((cyzc.zcdfje / 10000).toFixed(1))" :key="i">
                  <div class="numbg" v-if="item != ',' && item != '.' && item != '-'">
                    <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                  </div>
                  <div v-else>{{ item }}</div>
                </div>
              </div>
              <div class="txtS">万元</div>
            </div>
          </div>
          <div class="chartboxTwo">
            <div id="cyzcChart" style="width: 1099px; height: 570px"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import * as echarts from 'echarts'
import MainTitle from '@/components/MainTitle.vue'
import SubTitle from '@/components/SubTitle.vue'

// 全局变量声明
const baseURL = window.baseURL || { url: '' }

export default {
  name: 'DyCylCenter',
  components: {
    MainTitle,
    SubTitle
  },
  data() {
    return {
      leftBottom: [
        {
          name: '配套基金总额',
          num: '-',
          unit: '亿元',
        },
        {
          name: '专家人才总数',
          num: '-',
          unit: '人',
        },
        {
          name: '科创平台总数',
          num: '0',
          unit: '个',
        },
      ],
      data1: [
        {
          name: '链主培育企业',
          num: '50',
        },
        {
          name: '骨干企业',
          num: '50',
        },
        {
          name: '星火企业',
          num: '50',
        },
      ],
      cylId: null,
      companyTotal: 0,
      gsTotal: 0,
      ztz: 0,
      zs: 0,
      xms: 0,
      kcptArr: [],
      xnyqcVal: 0,
      cyzc: {
        zczs: 0,
        zcdfje: 0,
      },
      tabList: ['2024', '2025'],
      tabIndex: 0,
    }
  },

  mounted() {
    let that = this
    // 从路由参数中获取id，如果没有则默认为104
    that.cylId = this.$route.query.id ? parseInt(this.$route.query.id) : 104
    that.initData()
    this.getCyzcData()
  },

  watch: {
    // 监听路由变化，当id参数改变时重新获取数据
    '$route.query.id'(newId) {
      if (newId) {
        this.cylId = parseInt(newId)
        this.initData()
        this.getCyzcData()
      }
    }
  },

  methods: {
    tabClick(index) {
      this.tabIndex = index
      if (index == 0) {
        this.ztz = '80.47'
        this.zs = '59.03'
        this.xms = '57'
        this.pieEcharts(`hjfb-chartNew`, '73.36', ['#4FD6FF', '#0079E3'])
        getCsdnInterface1('csdn_qyhx58', { sstp: this.cylId, nd: 2024 }).then((res) => {
          let data2 = []
          if (res.data.data[0].gjbl == 0) {
            data2 = [
              {
                name: '新建项目',
                value: res.data.data[0].xjbl,
              },
              {
                name: '续建项目',
                value: res.data.data[0].xjbl1,
              },
            ]
          } else {
            data2 = [
              {
                name: '新建项目',
                value: res.data.data[0].xjbl,
              },
              {
                name: '续建项目',
                value: res.data.data[0].xjbl1,
              },
              {
                name: '改建项目',
                value: res.data.data[0].gjbl,
              },
            ]
          }
          this.getChart1('pieChart2', data2, '57')
        })
      } else if (index == 1) {
        this.ztz = '46.40'
        this.zs = '-'
        this.xms = '15'
        this.pieEcharts(`hjfb-chartNew`, '0', ['#4FD6FF', '#0079E3'])

        let data2 = [
          {
            name: '义乌市',
            value: 0.1333,
          },
          {
            name: '金东区',
            value: 0.0667,
          },
          {
            name: '开发区',
            value: 0.2667,
          },
          {
            name: '武义县',
            value: 0.0667,
          },
          {
            name: '兰溪市',
            value: 0.133,
          },
          {
            name: '婺械区',
            value: 0.3333,
          },
        ]
        this.getChart1('pieChart2', data2, '15')
      }
    },

    showPyqyDialog() {
      const that = this
      window.parent.lay.openIframe({
        type: 'openIframe',
        name: 'zdpyqy-list',
        src: baseURL.url + '/static/citybrain/qyzf/pages/zdpyqy-list.html',
        left: '0',
        top: '0',
        width: '7680px',
        height: '2160px',
        zIndex: '9999',
        argument: {
          type: 'newPyqyMessage',
          id: that.cylId,
        },
      })
    },

    showXmqdDialog() {
      const that = this
      window.parent.lay.openIframe({
        type: 'openIframe',
        name: 'zdxmqd-list',
        src: baseURL.url + '/static/citybrain/qyzf/pages/zdxmqd-list.html',
        left: '0',
        top: '0',
        width: '7680px',
        height: '2160px',
        zIndex: '9999',
        argument: {
          type: 'newXmqdMessage',
          id: that.cylId,
          nd: that.tabIndex == 0 ? '2024' : '2025',
        },
      })
    },

    showDialog() {
      const that = this
      window.parent.lay.openIframe({
        type: 'openIframe',
        name: 'zdxm-list',
        src: baseURL.url + '/static/citybrain/qyzf/pages/zdxm-list.html',
        left: '0',
        top: '0',
        width: '7680px',
        height: '2160px',
        zIndex: '9999',
        argument: {
          type: 'newMessage',
          id: that.cylId,
        },
      })
    },

    initData() {
      getCsdnInterface1('csdn_qyhx22', { ywwd2: this.cylId }).then((res) => {
        this.xnyqcVal = res.data.data[0] ? res.data.data[0].qy_num : 0
      })
      getCsdnInterface1('csdn_qyhx2', { sstp: this.cylId }).then((res) => {
        if (res.data.data.length != 0) {
          this.companyTotal = res.data.data[0].tjz
        } else {
          this.companyTotal = 0
        }
      })
      getCsdnInterface1('csdn_qyhx41', { sstp: this.cylId }).then((res) => {
        if (res.data.data.length != 0 && res.data.data[0]) {
          this.gsTotal = res.data.data[0].zs
        } else {
          this.gsTotal = 0
        }
      })
      getCsdnInterface1('csdn_qyhx45', { sstp: this.cylId }).then((res) => {
        this.data1[0].num = res.data.data[0].lzpyqys || 0
        this.data1[1].num = res.data.data[0].ggqys || 0
        this.data1[2].num = res.data.data[0].xhqys || 0
        this.drawCharts1('pieChart1', this.data1)
      })

      getCsdnInterface1('qyhx_cyl_zdxmtz', { sstp: this.cylId }).then((res) => {
        this.ztz = '80.47'
        this.zs = '59.03'
        this.xms = '57'
        this.pieEcharts(`hjfb-chartNew`, '73.36', ['#4FD6FF', '#0079E3'])
      })

      getCsdnInterface1('csdn_qyhx58', { sstp: this.cylId, nd: 2024 }).then((res) => {
        let data2 = []
        if (res.data.data[0].gjbl == 0) {
          data2 = [
            {
              name: '新建项目',
              value: res.data.data[0].xjbl,
            },
            {
              name: '续建项目',
              value: res.data.data[0].xjbl1,
            },
          ]
        } else {
          data2 = [
            {
              name: '新建项目',
              value: res.data.data[0].xjbl,
            },
            {
              name: '续建项目',
              value: res.data.data[0].xjbl1,
            },
            {
              name: '改建项目',
              value: res.data.data[0].gjbl,
            },
          ]
        }
        this.getChart1('pieChart2', data2, '57')
      })

      getCsdnInterface1('csdn_qyhx62', {
        sstp: this.cylId,
      }).then((res) => {
        this.leftBottom[2].num = res.data.data[0].zs
        let xValue = [res.data.data[0].hyzs, res.data.data[0].qyzs]
        this.getChartsOne(xValue)
      })

      if (this.cylId == 104) {
        this.leftBottom[0].num = 68
        this.leftBottom[1].num = 6
      }
    },

    drawCharts1(id, echartData) {
      let that = this
      echarts.init(document.getElementById(id)).dispose()
      let myChart = echarts.init(document.getElementById(id))
      let imgUrl = '/static/citybrain/qyzf/img/cyl/pieBg2.png'
      let data = echartData.map((item) => {
        return {
          name: item.name,
          value: item.num,
        }
      })
      let arrName = this.getArrayValue(data, 'name')
      let arrValue = this.getArrayValue(data, 'value')
      let sumValue = eval(arrValue.join('+'))
      let objData = this.array2obj(data, 'name')
      let optionData = this.getData(data)

      let sum = 0
      data.forEach((x) => {
        sum += parseInt(x.value)
      })
      let option = {
        color: ['#0061FF', '#14C9C9', '#E77930'],
        title: {
          text: sum,
          subtext: '总数',
          x: '30.5%',
          y: '40%',
          textStyle: {
            fontSize: 56,
            color: '#fff',
          },
          subtextStyle: {
            color: '#fff',
            fontSize: 36,
          },
          textAlign: 'center',
        },
        legend: {
          show: true,
          top: '22%',
          left: '50%',
          data: arrName,
          icon: 'circle',
          itemWidth: 15,
          itemHeight: 15,
          width: 20,
          padding: [0, 5],
          itemGap: 50,
          formatter: function (name) {
            return '{title|' + name + '}    {value|' + objData[name].value + '家}'
          },
          textStyle: {
            rich: {
              title: {
                fontSize: 42,
                color: '#fff',
              },
              value: {
                fontSize: 42,
                color: '#FFC460',
              },
            },
          },
          selectedMode: 'multiple',
          selected: {
            链主培育企业: true,
            骨干企业: true,
            星火企业: true,
          },
        },
        tooltip: {
          show: false,
          trigger: 'item',
          formatter: '{a}<br>{b}:{c}({d}家)',
          textStyle: {
            color: 'rgba(212, 232, 254, 1)',
            fontSize: 28,
          },
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
        },
        color: ['#00b1ec', '#21d8db', '#FFC460', '#a2d54d'],
        grid: {
          top: '0%',
          bottom: '0%',
          left: '0%',
          right: '0%',
          containLabel: false,
        },
        yAxis: [
          {
            type: 'category',
            inverse: true,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              inside: true,
              textStyle: {
                color: '#fff',
                fontSize: 20,
              },
              show: false,
            },
            data: optionData.yAxis,
          },
        ],
        xAxis: [
          {
            show: false,
          },
        ],
        graphic: [
          {
            z: 4,
            type: 'image',
            id: 'logo',
            left: '13.5%',
            top: '5%',
            z: -10,
            bounding: 'raw',
            rotation: 0,
            origin: [-30, 30],
            scale: [1, 1],
            style: {
              image: imgUrl,
              opacity: 1,
            },
          },
        ],
        series: optionData.series,
      }

      myChart.setOption(option, true)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
      myChart.on('legendselectchanged', function (params) {
        console.log('Selected:', params.selected)
        myChart.setOption({
          legend: { selected: { [params.name]: true } },
        })
      })
    },

    getArrayValue(array, key) {
      var key = key || 'value'
      var res = []
      if (array) {
        array.forEach(function (t) {
          res.push(t[key])
        })
      }
      return res
    },

    array2obj(array, key) {
      var resObj = {}
      for (var i = 0; i < array.length; i++) {
        resObj[array[i][key]] = array[i]
      }
      return resObj
    },

    getData(data) {
      var res = {
        series: [],
        yAxis: [],
      }
      let arrValue = this.getArrayValue(data, 'value')
      let sumValue = eval(arrValue.join('+'))

      for (let i = 0; i < data.length; i++) {
        res.series.push({
          type: 'pie',
          clockWise: false,
          hoverAnimation: false,
          radius: [90 - i * 15 + '%', 80 - i * 15 + '%'],
          center: ['30.5%', '53%'],
          label: {
            show: false,
          },
          itemStyle: {
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            borderWidth: 5,
          },
          data: [
            {
              value: data[i].value,
              name: data[i].name,
            },
            {
              value: sumValue - data[i].value * 2,
              name: '',
              itemStyle: {
                color: 'rgba(0,0,0,0)',
                borderWidth: 0,
              },
              tooltip: {
                show: false,
              },
              hoverAnimation: false,
            },
          ],
        })
        res.series.push({
          name: '',
          type: 'pie',
          silent: true,
          z: 1,
          clockWise: false,
          hoverAnimation: false,
          radius: [90 - i * 15 + '%', 80 - i * 15 + '%'],
          center: ['30.5%', '53%'],
          label: {
            show: false,
          },
          itemStyle: {
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            borderWidth: 5,
          },
          data: [
            {
              value: 7.5,
              itemStyle: {
                color: '#003b4f6b',
                borderWidth: 0,
              },
              tooltip: {
                show: false,
              },
              hoverAnimation: false,
            },
            {
              value: 2.5,
              name: '',
              itemStyle: {
                color: 'rgba(0,0,0,0)',
                borderWidth: 0,
              },
              tooltip: {
                show: false,
              },
              hoverAnimation: false,
            },
          ],
        })
        res.yAxis.push(((data[i].value / sumValue) * 100).toFixed(2) + '%')
      }
      return res
    },

    getChart1(id, datas, xms) {
      echarts.init(document.getElementById(id)).dispose()
      let MyEchart = echarts.init(document.getElementById(id))
      let color = ['#ff9b58', '#00c0ff', '#82e252', '#22e8e8', '#F0E68C', '#F08080']
      let imgUrl = '/static/citybrain/qyzf/img/cyl/pieBg.png'

      let option = {
        color: color,
        title: {
          text: xms,
          subtext: '总数',
          x: '28%',
          y: '37%',
          textStyle: {
            fontSize: 56,
            color: '#fff',
          },
          subtextStyle: {
            color: '#fff',
            fontSize: 36,
          },
        },
        legend: {
          show: true,
          top: 'middle',
          left: '55%',
          itemWidth: 15,
          itemHeight: 15,
          width: 50,
          icon: 'circle',
          padding: [0, 5],
          itemGap: 15,
          formatter: (name, i) => {
            let data = option.series[0].data
            let p = data.find((it) => it.name == name).value * 100
            return '{title|' + name + '}   {value|' + p.toFixed(0) + '%}'
          },
          textStyle: {
            rich: {
              title: {
                fontSize: 42,
                color: '#fff',
              },
              value: {
                fontSize: 42,
                color: '#FFC460',
              },
            },
          },
        },
        tooltip: {
          show: false,
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '24',
          },
          trigger: 'item',
          formatter: '{b}:{c}({d}%)',
        },
        graphic: [
          {
            z: 4,
            type: 'image',
            id: 'logo',
            left: '23.5%',
            top: '22%',
            z: -10,
            bounding: 'raw',
            rotation: 0,
            origin: [-30, 30],
            scale: [0.65, 0.65],
            style: {
              image: imgUrl,
              opacity: 1,
            },
          },
        ],
        series: [
          {
            type: 'pie',
            radius: ['65%', '85%'],
            center: ['32%', '50%'],
            data: datas,
            hoverAnimation: true,
            label: {
              show: false,
            },
            itemStyle: {
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
            },
          },
        ],
      }
      MyEchart.setOption(option, true)
      MyEchart.getZr().on('mousemove', (param) => {
        MyEchart.getZr().setCursorStyle('default')
      })
    },

    pieEcharts(id, data, color) {
      var myChart = echarts.init(document.getElementById(id))
      var option = {
        title: [
          {
            text: '完成度',
            x: 'center',
            top: '25%',
            textStyle: {
              fontSize: '28',
              color: '#FFFFFF',
            },
            subtext: data + '%',
            x: 'center',
            top: '35%',
            subtextStyle: {
              fontSize: '28',
              color: '#FFFFFF',
            },
          },
        ],
        polar: {
          radius: ['80%', '95%'],
          center: ['50%', '50%'],
        },
        angleAxis: {
          max: 100,
          show: false,
        },
        radiusAxis: {
          type: 'category',
          show: true,
          axisLabel: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            name: '',
            type: 'bar',
            roundCap: true,
            barWidth: 90,
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(66, 66, 66, .3)',
            },
            data: [data],
            coordinateSystem: 'polar',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  {
                    offset: 0,
                    color: color[1],
                  },
                  {
                    offset: 1,
                    color: color[0],
                  },
                ]),
              },
            },
          },
        ],
      }
      myChart.setOption(option)
    },

    getChartsOne(xValue) {
      let myChart = echarts.init(document.getElementById('chartZztId'))
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: 'rgba(9, 24, 48, 0.5)',
          borderColor: 'rgba(75, 253, 238, 0.4)',
          textStyle: {
            color: '#CFE3FC',
            fontSize: '36',
          },
          borderWidth: 1,
        },
        grid: {
          top: '25%',
          right: '0%',
          left: '15%',
          bottom: '10%',
        },
        xAxis: [
          {
            type: 'category',
            data: ['行业级科创平台', '企业级科创平台'],
            axisLine: {
              lineStyle: {
                width: 3,
                color: 'rgba(122, 163, 197, 1)',
              },
            },
            axisLabel: {
              color: '#fff',
              textStyle: {
                fontSize: 28,
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            name: '科创平台分类（个）',
            nameTextStyle: {
              color: '#fff',
              fontSize: 28,
              padding: [0, 0, 20, 0],
            },
            axisLabel: {
              formatter: '{value}',
              color: '#fff',
              textStyle: {
                fontSize: 28,
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#FFFFFF',
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(122, 163, 197, 0)',
              },
            },
          },
        ],
        series: [
          {
            type: 'bar',
            data: xValue,
            barWidth: '20px',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(0, 177, 255, 1)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(0, 177, 255, 0)',
                    },
                  ],
                  false
                ),
                shadowColor: 'rgba(0, 177, 255, 1)',
                shadowBlur: 4,
              },
            },
            label: {
              normal: {
                show: false,
              },
            },
          },
        ],
      }
      myChart.setOption(option, true)
    },

    setChart4(id, chartData, unit, name) {
      let myChart = echarts.init(document.getElementById(id))
      let xdata = chartData.map((item) => {
        return item.yf
      })
      let ydata1 = chartData.map((item) => {
        return item.zcts
      })
      let ydata2 = chartData.map((item) => {
        return item.dfje
      })
      let option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          axisPointer: {
            type: 'shadow',
          },
          textStyle: {
            color: 'white',
            fontSize: '32',
          },
        },
        graphic: {
          type: 'text',
          left: 'center',
          top: 'middle',
          silent: true,
          invisible: chartData.length,
          style: {
            fill: 'white',
            text: '暂无数据',
            font: '32px system-ui',
          },
        },
        grid: {
          top: '15%',
          right: '15%',
          left: '20%',
          bottom: '15%',
        },
        xAxis: [
          {
            type: 'category',
            data: xdata,
            axisLine: {
              lineStyle: {
                color: 'rgba(122, 163, 197, 1)',
              },
            },
            axisLabel: {
              margin: 20,
              color: '#fff',
              textStyle: {
                fontSize: 28,
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            name: unit,
            nameTextStyle: {
              color: '#fff',
              fontSize: 28,
              padding: [0, 40, 20, 0],
            },
            axisLabel: {
              formatter: '{value}',
              color: '#fff',
              textStyle: {
                fontSize: 28,
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#FFFFFF',
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(122, 163, 197, 0)',
              },
            },
          },
          {
            show: true,
            name: '元',
            type: 'value',
            nameTextStyle: {
              fontSize: 28,
              color: '#fff',
              padding: [0, 0, 20, 40],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#fff',
              },
            },
          },
        ],
        series: [
          {
            name: name,
            type: 'bar',
            data: ydata1,
            barWidth: '20px',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(0, 177, 255, 1)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(0,77,167,0)',
                    },
                  ],
                  false
                ),
                shadowColor: 'rgba(0,160,221,1)',
                shadowBlur: 4,
              },
            },
            label: {
              normal: {
                show: false,
              },
            },
          },
          {
            name: '金额',
            type: 'line',
            itemStyle: {
              color: '#FF7D00',
            },
            lineStyle: {
              color: '#FF7D00',
            },
            yAxisIndex: 1,
            data: ydata2,
          },
        ],
      }
      myChart.setOption(option, true)
    },

    getCyzcData() {
      getCsdnInterface1('qyhx_cyl_jnzcsjdfje').then((res) => {
        this.cyzc.zczs = res.data.data.reduce((accumulator, current) => {
          return accumulator + (Number(current.zcts) || 0)
        }, 0)

        this.cyzc.zcdfje = res.data.data.reduce((accumulator, current) => {
          return accumulator + (Number(current.dfje) || 0)
        }, 0)

        this.setChart4('cyzcChart', res.data.data, '条', '数量')
      })
    },
  },
}
</script>

<style lang="less" scoped>
* {
  margin: 0;
  padding: 0;
}

.container {
  width: 2400px;
  height: 1904px;
  padding: 10px 55px 0px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.con {
  width: 100%;
  height: 50%;
  display: flex;
  justify-content: space-between;
}

.leftTopList {
  margin: 80px 0;
  display: flex;
  justify-content: space-between;
}

.leftTopItem {
  display: flex;
}

.leftTopItem .left {
  width: 157px;
  height: 184px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.leftTopItem .right {
  width: 367px;
  height: 184px;
  padding: 23px 48px;
  box-sizing: border-box;
}

.leftTopItem .name {
  font-size: 32px;
  color: #dcefff;
  margin-bottom: 5px;
}

.leftTopItem .num {
  font-family: DIN, DIN;
  font-weight: bold;
  font-size: 56px;
  line-height: 56px;
  letter-spacing: 1px;
  text-shadow: 0px 8px 20px rgba(0, 0, 0, 0.4039);
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.leftTopItem .unit {
  font-size: 28px;
}

.leftTopItem .rate {
  display: flex;
  justify-content: space-between;
  font-size: 24px;
  color: #d8d8d8;
}

.leftTopItem .rate .value {
  color: #f7ba1e;
}

.rightTopLine {
  display: flex;
  align-items: center;
  font-size: 30px;
  color: #dcefff;
  margin-bottom: 33px;
}

.rightTopLine .num {
  font-weight: 700;
  white-space: no-wrap;
}

.leftBottomList {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 20px;
}

.leftBottomItem {
  background: url('@/assets/img/dyCyl/leftBottomBg.png');
  width: 310px;
  height: 220px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  font-size: 36px;
  color: #ffffffaa;
}

.leftBottomItem .name {
  margin-top: 50px;
}

.leftBottomItem .num {
  width: 100%;
  font-family: DINA, DINA;
  font-size: 64px;
  line-height: 56px;
  letter-spacing: 1px;
  text-shadow: 0px 8px 20px rgba(0, 0, 0, 0.4039);
  text-align: center;
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-top: 20px;
}

.leftBottomItem .unit {
  font-size: 32px;
}

.chartTitle {
  width: 100%;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 700;
  font-size: 42px;
  letter-spacing: 4px;
  text-align: center;
  background: linear-gradient(90deg, #ffffff 0%, #b9ccff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0px 0 20px 0;
}

.chartbox {
  width: 1017px;
  height: 471px;
  background: url('@/assets/img/dyCyl/rightBottom.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
}

.chartboxTwo {
  flex: 1;
  width: 100%;
}

.chartbox .item1,
.chartbox .item2,
.chartbox .item3,
.chartbox .item4 {
  font-size: 32px;
  color: #ffffff;
  text-align: center;
}

.chartbox .item1 {
  position: absolute;
  top: 68px;
  left: 150px;
}

.chartbox .item2 {
  position: absolute;
  bottom: 68px;
  left: 150px;
}

.chartbox .item3 {
  position: absolute;
  top: 68px;
  right: 150px;
}

.chartbox .item4 {
  position: absolute;
  bottom: 68px;
  right: 150px;
}

.chartZzt {
  width: 100%;
  flex: 1;
}

.chartZztClass {
  width: 100%;
  height: 100%;
}

.xm_box {
  display: flex;
  align-items: center;
}

.xm_box .xm_item {
  display: flex;
  align-items: center;
}

.xm_box .xm_item .xm_item_left {
  width: 184px;
  height: 185px;
}

.xm_box .xm_item .xm_item_left img {
  width: 184px;
  height: 185px;
}

.xm_box .xm_item .xm_item_right {
  margin-left: 16px;
}

.xm_box .xm_item .xm_item_right .xm_txt {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 32px;
  color: #dcefff;
  line-height: 32px;
  text-align: center;
}

.xm_box .xm_item .xm_item_right .xm_img img {
  width: 240px;
  height: 26px;
  margin-bottom: 24px;
}

.xm_box .xm_item .xm_item_right .xm_txt1 {
  font-family: DIN, DIN;
  font-weight: 700;
  font-size: 56px;
  line-height: 56px;
  letter-spacing: 1px;
  text-shadow: 0px 8px 20px rgba(0, 0, 0, 0.4039);
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
}

.newChartWrap {
  width: 206px;
  height: 185px;
}

.newflex {
  display: flex;
  align-items: center;
}

.tab_item {
  width: 180px;
  height: 50px;
  font-size: 36px;
  font-weight: 400;
  color: #bfbcbc;
  background: rgba(0, 74, 166, 0.3);
  border: 2px solid #215293;
  text-align: center;
  line-height: 50px;
  cursor: pointer;
}

.tab_item:nth-child(2) {
  border-radius: 15px 0px 0px 15px;
}

.tab_item:nth-child(3) {
  border-radius: 0px 15px 15px 0px;
}

.tab_active {
  color: #ffffff;
  font-weight: 700;
  background: rgba(0, 74, 166, 0.8);
}

.mr {
  margin-right: 50px;
}

/* 标题样式已移至组件中 */

.item-contents {
  width: 100%;
  height: 770px;
  position: relative;
}

.cyzyClass {
  display: flex;
  flex-direction: column;
}

/* 副标题样式已移至组件中 */

.dotClass {
  width: 60px;
  height: 90px;
  cursor: pointer;
}

.upTwoClass {
  width: 100%;
  height: 230px;
  background: linear-gradient(90deg, rgba(0, 121, 227, 0) 10%, rgba(28, 148, 249, 0.3) 50%, rgba(0, 121, 227, 0) 90%);
  margin: 29px 0px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.upTwoClassTwo {
  width: 100%;
  height: 230px;
  background: linear-gradient(90deg, rgba(0, 121, 227, 0) 10%, rgba(28, 148, 249, 0.3) 50%, rgba(0, 121, 227, 0) 90%);
  margin: 29px 0px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.upsClass {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upsClassTwo {
  display: flex;
  justify-content: center;
  align-items: center;
}

.txtS {
  font-size: 32px;
  color: #ffffff;
}

.countCard {
  margin: 0 34px;
}

.number {
  display: inline-block;
  font-size: 40px;
  color: #fff;
  font-weight: 400;
}

.number .numbg {
  display: inline-block;
  width: 55px;
  height: 75px;
  line-height: 75px;
  text-align: center;
  background: url('@/assets/img/dyCyl/num.png') no-repeat;
  background-size: contain;
  margin: 0 4px;
}

.numbg span {
  font-size: 64px;
  background: linear-gradient(180deg, #ffc460 0%, #fd852e 100%);
  font-weight: bolder;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.unitNew {
  font-size: 28px;
}

.con-item {
  width: 48%;
  overflow: hidden;
}

.ht {
  height: 800px;
}

.hs {
  height: 830px;
}

.newh {
  height: 50% !important;
}
</style>