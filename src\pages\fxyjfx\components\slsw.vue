<template>
  <div class="slsw-wrap" v-if="visible">
    <div class="content">
      <div class="close" @click="visible = false">
        ×
      </div>

      <!-- 顶部标签切换 -->
      <div class="tab">
        <div
          v-for="(ele, index) in tabData"
          :key="index"
          class="tab-item"
          :class="[
            index === tabIndex
              ? index === 0
                ? 'left-bg-active'
                : 'right-bg-active'
              : index === 0
              ? 'left-bg'
              : 'right-bg',
          ]"
          @click="togTabFun(index)"
        >
          <span :class="tabIndex === index ? 's-c-yellow-gradient' : 's-c-blue-gradient'">{{ ele }}</span>
        </div>
      </div>

      <!-- 小标题 -->
      <div class="title_box">
        <div class="left_title">
          <nav class="s-m-t-30">
            <p class="hearder_h2">
              <img src="@/pages/fxyjfx/img/slsw/title-l.png" alt="" />
              <span>昨日{{ tabData[tabIndex] }}城市流入情况</span>
              <img src="@/pages/fxyjfx/img/slsw/title-r.png" alt="" />
            </p>
          </nav>
        </div>
        <div class="right_title">
          <nav class="s-m-t-30">
            <p class="hearder_h2">
              <img src="@/pages/fxyjfx/img/slsw/title-l.png" alt="" />
              <span>昨日{{ tabData[tabIndex] }}城市流出情况</span>
              <img src="@/pages/fxyjfx/img/slsw/title-r.png" alt="" />
            </p>
          </nav>
        </div>
      </div>

      <!-- 中间迁移图 -->
      <div class="top-qyt">
        <div id="mainMapLr" style="width: 1629px; height: 1257px"></div>
        <div id="mainMapLc" style="width: 1629px; height: 1257px"></div>
      </div>

      <!-- 底部轮播 -->
      <div class="lb-con">
        <div class="bottom-lbt">
          <div class="lb-css" style="position: relative">
            <img
              src="@/pages/fxyjfx/img/slsw/swiper-left.png"
              alt=""
              width="82"
              height="113"
              class="btn-left mouse-pointer"
              @click="left_next"
            />
            <el-carousel
              ref="part1_left"
              arrow="never"
              :autoplay="false"
              indicator-position="outside"
              style="height: 240px; overflow: hidden"
            >
              <el-carousel-item
                v-for="(item, i) in listLeft"
                :key="i"
                style="height: 240px; display: flex; align-items: center; justify-content: space-between"
              >
                <div class="lb-item" v-for="item in listLeft[i]" :key="item.pm">
                  <div class="num">{{ item.pm }}</div>
                  <div class="text-css">
                    <div class="name s-c-yellow-gradient" :title="item.name">{{ item.name }}</div>
                    <div class="value">{{ item.num }}人</div>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
            <img
              src="@/pages/fxyjfx/img/slsw/swiper-right.png"
              alt=""
              width="82"
              height="113"
              class="btn-right mouse-pointer"
              @click="left_prev"
            />
          </div>
        </div>
        <div class="bottom-lbt">
          <div class="lb-css" style="position: relative">
            <img
              src="@/pages/fxyjfx/img/slsw/swiper-left.png"
              alt=""
              width="82"
              height="113"
              class="btn-left mouse-pointer"
              @click="right_next"
            />
            <el-carousel
              ref="part1_right"
              arrow="never"
              :autoplay="false"
              indicator-position="outside"
              style="height: 240px; overflow: hidden"
            >
              <el-carousel-item
                v-for="(item, i) in listRight"
                :key="i"
                style="height: 240px; display: flex; align-items: center; justify-content: space-between"
              >
                <div class="lb-item" v-for="item in listRight[i]" :key="item.pm">
                  <div class="num">{{ item.pm }}</div>
                  <div class="text-css">
                    <div class="name s-c-yellow-gradient" :title="item.name">{{ item.name }}</div>
                    <div class="value">{{ item.num }}人</div>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
            <img
              src="@/pages/fxyjfx/img/slsw/swiper-right.png"
              alt=""
              width="82"
              height="113"
              class="btn-right mouse-pointer"
              @click="right_prev"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import * as echarts from 'echarts'
export default {
  name: 'slsw',
  props: {
    title: { type: String, default: '城市流入、流出情况' },
    initialTab: { type: Number, default: 0 },
    visible: false,
  },
  data() {
    return {
      tabData: ['省内', '省外'],
      tabIndex: 0,
      // 省内流入流出
      slIn: [],
      slOut: [],
      // 省外流入流出
      swIn: [],
      swOut: [],
      // 左边的轮播列表数据
      leftListData: [],
      leftListNum: 5,
      // 右边的轮播列表数据
      RightListData: [],
      rightListNum: 5,
      qxMapData: [
        { coord: [119.393576, 30.057459], name: '杭州市', num: '', pm: '' },
        { coord: [121.409792, 29.700388], name: '宁波市', num: '', pm: '' },
        { coord: [121.114181, 28.772005], name: '台州市', num: '', pm: '' },
        { coord: [120.702112, 29.897117], name: '绍兴市', num: '', pm: '' },
        { coord: [120.672111, 28.000575], name: '温州市', num: '', pm: '' },
        { coord: [118.769723, 28.921163], name: '衢州市', num: '', pm: '' },
        { coord: [119.422711, 28.10005], name: '丽水市', num: '', pm: '' },
        { coord: [119.653436, 29.084634], name: '金华市', num: '', pm: '' },
      ],
    }
  },
  computed: {
    listLeft() {
      let newArr = []
      for (let i = 0; i < this.leftListData.length; i += this.leftListNum) {
        newArr.push(this.leftListData.slice(i, i + this.leftListNum))
      }
      return newArr
    },
    listRight() {
      let newArr = []
      for (let i = 0; i < this.RightListData.length; i += this.rightListNum) {
        newArr.push(this.RightListData.slice(i, i + this.rightListNum))
      }
      return newArr
    },
  },
  mounted() {
    this.tabIndex = Number(this.initialTab) || 0
    this.initFun()
  },
  methods: {
    initFun() {
      // 1流出 0流入 省内
      getCsdnInterface1('/csrk_lrlc_sn', { typeCode: 0 }).then((a_in) => {
        console.log("11",a_in)
        let arr_in = []
        a_in.data.data.map((ele, index) => {
          let obj = {
            name: ele.cityName,
            num: ele.countNum,
            coord: [ele.gps.split(',')[0], ele.gps.split(',')[1]],
            pm: index + 1,
          }
          arr_in.push(obj)
        })
        this.leftListData = this.slIn = arr_in
        this.provinceChart('mainMapLr', 1, this.slIn, this.tabData[this.tabIndex])
      })
      getCsdnInterface1('/csrk_lrlc_sn', { typeCode: 1 }).then((a_out) => {
        let arr_out = []
        a_out.data.data.map((ele, index) => {
          let obj = {
            name: ele.cityName,
            num: ele.countNum,
            coord: [ele.gps.split(',')[0], ele.gps.split(',')[1]],
            pm: index + 1,
          }
          arr_out.push(obj)
        })
        this.RightListData = this.slOut = arr_out
        this.provinceChart('mainMapLc', 2, this.slOut, this.tabData[this.tabIndex])
      })
      // 省外
      getCsdnInterface1('/csrk_lrlc_sw', { typeCode: 0 }).then((res) => {
        let arr = []
        res.data.data.map((ele, index) => {
          let obj = {
            name: ele.cityName,
            num: ele.countNum,
            coord: [ele.gps.split(',')[0], ele.gps.split(',')[1]],
            pm: index + 1,
          }
          arr.push(obj)
        })
        this.swOut = arr
      })
      getCsdnInterface1('/csrk_lrlc_sw', { typeCode: 1 }).then((res) => {
        let arr = []
        res.data.data.map((ele, index) => {
          let obj = {
            name: ele.cityName,
            num: ele.countNum,
            coord: [ele.gps.split(',')[0], ele.gps.split(',')[1]],
            pm: index + 1,
          }
          arr.push(obj)
        })
        this.swIn = arr
      })
    },
    togTabFun(num) {
      this.tabIndex = num
      if (num == 0) {
        this.leftListData = this.slIn
        this.RightListData = this.slOut
        this.provinceChart('mainMapLr', 1, this.slIn, this.tabData[this.tabIndex])
        this.provinceChart('mainMapLc', 2, this.slOut, this.tabData[this.tabIndex])
      } else {
        this.leftListData = this.swIn
        this.RightListData = this.swOut
        this.drawQgMap('mainMapLr', this.swIn, 1)
        this.drawQgMap('mainMapLc', this.swOut, 0)
      }
    },
    left_prev() {
      this.$refs.part1_left.prev()
    },
    left_next() {
      this.$refs.part1_left.next()
    },
    right_prev() {
      this.$refs.part1_right.prev()
    },
    right_next() {
      this.$refs.part1_right.next()
    },

    provinceChart(dom, num, mapData, titleText) {
      echarts.init(document.getElementById(dom)).dispose()
      let chartDom = echarts.init(document.getElementById(dom))
      let outData = JSON.parse(JSON.stringify(this.qxMapData))
      let len = outData.length - 1
      let centerData = outData[len]

      getCsdnInterface1('/hjbh/rkzt/330000_full').then((res) => {
        echarts.registerMap('zj', res.data.data)
        let series = []
        let chinaDatas = mapData
        let sum = 0
        chinaDatas.forEach((e) => {
          sum += e.num
        })
        let option = {
          geo: {
            show: true,
            map: 'zj',
            roam: true,
            layoutSize: '90%',
            layoutCenter: ['50%', '50%'],
            itemStyle: {
              normal: {
                borderColor: 'rgba(147, 235, 248, 1)',
                borderWidth: 1,
                shadowOffsetX: -2,
                shadowOffsetY: 2,
                shadowBlur: 10,
              },
              emphasis: { areaColor: num == 1 ? '#389BB7' : '#7E6743' },
            },
          },
          series,
        }
        ;[[centerData.name, chinaDatas]].forEach((item) => {
          series.push(
            {
              type: 'lines',
              zlevel: 2,
              effect: { show: true, period: 4, trailLength: 0.02, symbol: 'arrow', symbolSize: 20 },
              lineStyle: { normal: { width: 6, opacity: 1, curveness: 0.3 } },
              data: this.convertData1(item[1], outData, centerData.coord, num),
            },
            {
              type: 'effectScatter',
              coordinateSystem: 'geo',
              zlevel: 2,
              rippleEffect: { period: 4, brushType: 'stroke', scale: 4 },
              label: {
                normal: {
                  show: true,
                  position: 'right',
                  formatter: (p) => p.data.pm + '' + p.data.name + '{a| ' + p.data.num + '}人',
                  color: '#fff',
                  fontSize: 36,
                  rich: { a: { color: 'yellow', fontSize: 36 } },
                },
              },
              symbol: 'circle',
              symbolSize: () => 10,
              data: item[1].map((d) => ({ name: d.name, value: d.coord, num: d.num, pm: d.pm })),
            },
            {
              type: 'scatter',
              coordinateSystem: 'geo',
              zlevel: 2,
              rippleEffect: { period: 4, brushType: 'stroke', scale: 4 },
              label: {
                normal: {
                  show: true,
                  position: 'left',
                  color: '#fff',
                  formatter: '{b}\n' + sum,
                  textStyle: { color: '#f00', fontSize: 40 },
                },
              },
              symbol: 'pin',
              symbolSize: 60,
              itemStyle: { normal: { color: '#f44336' } },
              data: [{ name: item[0], value: outData[len].coord }],
            }
          )
          chartDom.setOption(option)
          chartDom.getZr().on('mousemove', () => {
            chartDom.getZr().setCursorStyle('default')
          })
        })
      })
    },
    convertData1(data, chinaGeoCoordMap, id, num) {
      var res = []
      for (var i = 0; i < data.length; i++) {
        var dataItem = data[i]
        var fromCoord = dataItem.coord
        var toCoord = id
        if (fromCoord && toCoord && num == 2) {
          res.push([{ coord: toCoord }, { coord: fromCoord, value: dataItem.num }])
        } else if (fromCoord && toCoord && num == 1) {
          res.push([{ coord: fromCoord, value: dataItem.num }, { coord: toCoord }])
        }
      }
      return res
    },
    drawQgMap(dom, mapData, num) {
      let outData = JSON.parse(JSON.stringify(this.qxMapData))
      echarts.init(document.getElementById(dom)).dispose()
      const myChart3 = echarts.init(document.getElementById(dom))
      let series = []
      let option = {
        geo: {
          map: 'china',
          zoom: 1.2,
          roam: true,
          itemStyle: {
            normal: {
              borderColor: '#00ffff',
              borderWidth: 1,
              opacity: 0.8,
              shadowBlur: 20,
              shadowColor: '#006dda',
              shadowOffsetX: 5,
              shadowOffsetY: 5,
            },
            emphasis: { areaColor: num == 1 ? '#389BB7' : '#7E6743' },
          },
        },
        series,
      }
      ;[['金华', mapData]].forEach((item) => {
        series.push(
          {
            type: 'lines',
            zlevel: 2,
            effect: { show: true, period: 4, trailLength: 0.02, symbol: 'arrow', symbolSize: 20 },
            lineStyle: { normal: { width: 6, opacity: 1, curveness: 0.3 } },
            data: this.convertData(item[1], mapData, [119.653436, 29.084634], dom, num),
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            zlevel: 2,
            rippleEffect: { period: 4, brushType: 'stroke', scale: 4 },
            label: {
              normal: {
                show: true,
                position: 'right',
                formatter: (p) => p.data.pm + '' + p.data.name + '{a| ' + p.data.num + '}人',
                color: '#fff',
                fontSize: 36,
                rich: { a: { color: 'yellow', fontSize: 36 } },
              },
            },
            symbol: 'circle',
            symbolSize: () => 10,
            data: item[1].map((d) => ({ name: d.name, value: d.coord, num: d.num, pm: d.pm })),
          },
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            zlevel: 2,
            rippleEffect: { period: 4, brushType: 'stroke', scale: 4 },
            label: {
              normal: { show: true, position: 'left', formatter: '{b}', textStyle: { color: '#f00', fontSize: 40 } },
            },
            symbol: 'pin',
            symbolSize: 60,
            itemStyle: { normal: { color: '#f44336' } },
            data: [{ name: '金华', value: outData[outData.length - 1].coord }],
          }
        )
      })
      myChart3.setOption(option)
      myChart3.getZr().on('mousemove', () => {
        myChart3.getZr().setCursorStyle('default')
      })
    },
    convertData(data, chinaGeoCoordMap, id, dom, num) {
      if (num == 1) {
        var res = []
        for (var i = 0; i < data.length; i++) {
          var dataItem = data[i]
          var fromCoord = dataItem.coord
          var toCoord = id
          if (fromCoord && toCoord) {
            res.push([{ coord: fromCoord, value: dataItem.num }, { coord: toCoord }])
          }
        }
        return res
      } else {
        var res = []
        for (var i = 0; i < data.length; i++) {
          var dataItem = data[i]
          var fromCoord = dataItem.coord
          var toCoord = id
          if (fromCoord && toCoord) {
            res.push([{ coord: toCoord, value: dataItem.num }, { coord: fromCoord }])
          }
        }
        return res
      }
    },
  },
}
</script>

<style scoped>
.slsw-wrap {
  position: absolute;
  background-image: url('@/pages/fxyjfx/img/slsw/dialog-bg.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 120% 120%;
  left: 26.5%;
  top: 5%;
  border: 2px solid #39439b; 
  z-index: 99999999;

}
.content {
  position: relative;
  width: 3590px;
  height: 1943px;
}
.tab {
  width: max-content;
  height: 70px;
  margin: 20px auto;
  background-image: url('@/pages/fxyjfx/img/slsw/tab_bg.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 136% 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
}
.tab-item {
  width: 282px;
  height: 70px;
  line-height: 70px;
  text-align: center;
  font-size: 34px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
}
.left-bg {
  background-image: url('@/pages/fxyjfx/img/slsw/tab-left-bg.png');
}
.left-bg-active {
  background-image: url('@/pages/fxyjfx/img/slsw/tab-left-bg-active.png') !important;
}
.right-bg {
  background-image: url('@/pages/fxyjfx/img/slsw/tab-right-bg.png');
}
.right-bg-active {
  background-image: url('@/pages/fxyjfx/img/slsw/tab-right-bg-active.png') !important;
}
.top-qyt {
  width: 96%;
  height: 1300px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.lb-con {
  width: 100%;
  height: 280px;
  padding: 0 50px;
  box-sizing: border-box;
  margin: 0 auto;
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
}
.bottom-lbt {
  width: 1707px;
  height: 100%;
  padding: 20px 50px;
  box-sizing: border-box;
  background: url('@/pages/fxyjfx/img/slsw/bottom-bg.png') no-repeat;
  background-size: 100% 100%;
}
.lb-css {
  width: 100%;
  height: 100%;
}
.lb-item {
  width: 320px;
  height: 180px;
  background: url('@/pages/fxyjfx/img/slsw/lb_item.png') no-repeat;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  font-size: 30px;
  color: #fff;
}
.text-css {
  margin-left: 20px;
}
.num {
  width: 120px;
  text-align: center;
  font-size: 50px;
  color: #fff;
  padding-left: 20px;
  box-sizing: border-box;
}
.value {
  font-size: 40px;
}
.btn-left {
  width: 78px;
  position: absolute;
  top: 65px;
  left: -60px;
  opacity: 0.75;
  z-index: 10;
}
.btn-right {
  width: 78px;
  position: absolute;
  top: 65px;
  right: -60px;
  opacity: 0.75;
  z-index: 10;
}
.title_box {
  width: 100%;
  padding: 0 50px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
}
.title_box > div {
  width: 1707px;
}
.hearder_h2 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 32px;
  font-size: 34px;
  font-weight: 500;
  text-shadow: 0px 4px 9px rgba(0, 0, 0, 0.29);
  background: linear-gradient(180deg, #caffff 0%, #00c0ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.hearder_h2 > span {
  margin: 0 20px;
  white-space: nowrap;
}
.mouse-pointer {
  cursor: pointer;
}
.close {
    width: 100px;
    height: 100px;
    color: #ffffff;
    cursor: pointer;
    right: -3550px;
    position: relative;
    font-size: 36px;
}
</style>
