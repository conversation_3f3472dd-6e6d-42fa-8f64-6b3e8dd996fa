<template>
  <div class="chart-item">
    <SubTitle title="结婚登记情况" />
    <div class="chart-container">
      <div id="marriageRegistration" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'

export default {
  name: 'MarriageRegistration',
  components: { SubTitle },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      const chart = this.$echarts.init(document.getElementById('marriageRegistration'))
      const months = ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月']
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          textStyle: { color: '#fff', fontSize: 14 },
        },
        grid: { left: '10%', right: '10%', top: '14%', bottom: '12%' },
        xAxis: {
          type: 'category',
          data: months,
          axisLabel: { color: '#fff', fontSize: 12 },
          axisLine: { lineStyle: { color: '#333' } },
        },
        yAxis: {
          type: 'value',
          axisLabel: { color: '#fff', fontSize: 12 },
          axisLine: { lineStyle: { color: '#333' } },
          splitLine: { lineStyle: { color: '#333' } },
        },
        series: [
          {
            type: 'bar',
            data: [60, 72, 55, 80, 66, 90, 110, 95, 88, 100, 120, 130],
            barWidth: 18,
            itemStyle: { color: '#ffb980' },
          },
        ],
      }
      chart.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>

