<template>
  <div class="chart-item">
    <SubTitle title="常住人口变化趋势" />
    <div class="chart-container">
      <div id="residentPopulationTrend" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'

export default {
  name: 'ResidentPopulationTrend',
  components: { SubTitle },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      const chart = this.$echarts.init(document.getElementById('residentPopulationTrend'))
      const months = ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月']
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          textStyle: { color: '#fff', fontSize: 14 },
        },
        grid: { left: '10%', right: '10%', top: '14%', bottom: '12%' },
        xAxis: {
          type: 'category',
          data: months,
          axisLabel: { color: '#fff', fontSize: 12 },
          axisLine: { lineStyle: { color: '#333' } },
        },
        yAxis: {
          type: 'value',
          axisLabel: { color: '#fff', fontSize: 12 },
          axisLine: { lineStyle: { color: '#333' } },
          splitLine: { lineStyle: { color: '#333' } },
        },
        series: [
          {
            name: '常住人口',
            type: 'line',
            smooth: true,
            data: [120, 132, 101, 134, 90, 230, 210, 260, 240, 280, 300, 320],
            lineStyle: { color: '#00c0ff', width: 2 },
            itemStyle: { color: '#00c0ff' },
            areaStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(0, 192, 255, 0.3)' },
                { offset: 1, color: 'rgba(0, 192, 255, 0)' },
              ]),
            },
          },
        ],
      }
      chart.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>

