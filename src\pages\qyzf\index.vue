<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-04-14 11:38:22
 * @LastEditors: wjb
 * @LastEditTime: 2025-06-11 15:25:44
-->
<template>
  <div class="qyzf-container">
    <div class="inneLeft">
      <wrapbox bg="left">
        <qyzfLeft class="animate__animated animate__fadeInLeft"  @openDialog="openQyInfo"/>
      </wrapbox>
    </div>
    <div class="midBottom animate__animated animate__fadeIn">
      <qyzfCenter />
    </div>
    <div class="innerRight">
      <wrapbox bg="right">
        <qyzfRight class="animate__animated animate__fadeInLeft" />
      </wrapbox>
    </div>

    <!-- 弹窗 -->
    <div v-if="qyInfoVisible">
      <qyInfo :visible="qyInfoVisible" :qylx="qylx" @close="qyInfoVisible = false" @openDialog="openQyhx"></qyInfo>
    </div>
    <div v-if="visible">
      <qyhxDetail :allMessage="allMessage" :visible="visible" @close="visible = false"></qyhxDetail>
    </div>
  </div>
</template>

<script>
import wrapbox from '@/components/wrapbox'
import qyzfLeft from '@/pages/qyzf/components/qyzfLeft'
import qyzfCenter from '@/pages/qyzf/components/qyzfCenter'
import qyzfRight from '@/pages/qyzf/components/qyzfRight'
import qyInfo from '@/pages/qyzf/components/qyInfoDialog'
import qyhxDetail from '@/pages/qyzf/components/qyhxIndex'
export default {
  name: 'index',
  data() {
    return {
      visible: false,
      qyInfoVisible: false,
      qylx: '',
      allMessage: [],
    }
  },
  components: {
    wrapbox,
    qyzfLeft,
    qyzfCenter,
    qyzfRight,
    qyInfo,
    qyhxDetail,
  },
  computed: {},
  mounted() {},
  methods: {
    openQyInfo(name) {
      this.qyInfoVisible = true
      this.qylx = name.slice(0, 4)
    },
    openQyhx(value) {
      this.allMessage = value
      this.qyInfoVisible=false
      this.visible = true
    },
  },
  watch: {},
}
</script>

<style scoped lang="less">
.qyzf-container {
  position: relative;
  width: 100%;
  height: 100%;
  // z-index: 2;
  // background: url('~@/assets/application/dtBg.png') no-repeat center center;
  // background-size: 100% 100%;
  .inneLeft {
    position: absolute;
    top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .innerRight {
    position: absolute;
    top: 229px;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .midBottom {
    position: absolute;
    left: 2135px;
    top: 229px;
    z-index: 2;
  }
}
</style>
