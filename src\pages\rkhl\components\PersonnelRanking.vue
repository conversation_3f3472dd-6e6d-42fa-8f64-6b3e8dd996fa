<template>
  <div class="chart-item">
    <SubTitle title="人员排名" />
    <div class="chart-container">
      <div class="ranking-list">
        <div class="ranking-item" v-for="(item, index) in populationRanking" :key="index">
          <span class="rank-name">{{ item.name }}</span>
          <div class="rank-bar">
            <div class="bar-fill" :style="{ width: item.percentage + '%' }"></div>
          </div>
          <span class="rank-value">{{ item.value }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'PersonnelRanking',
  components: {
    SubTitle,
  },
  data() {
    return {
      populationRanking: [
        { name: '府河社区府河一', value: 1134, percentage: 85 },
        { name: '府河社区府河三', value: 2334, percentage: 70 },
        { name: '府城老城区府河', value: 1234, percentage: 60 },
        { name: '府城老城区府二', value: 914, percentage: 45 },
      ],
    }
  },
  mounted() {
    this.fetchRankingData()
  },
  methods: {
    // 获取人员排名数据
    async fetchRankingData() {
      try {
        // 获取镇街排名数据
        const response = await getCsdnInterface1('csrk_wgsj_alljdt10', { order: 1 })
        const data = response.data.data || []

        // 处理数据用于统计模式的排名列表
        const sortedData = data.sort((a, b) => (b.val || b.areaNUm || b.population_count) - (a.val || a.areaNUm || a.population_count))
        const maxValue = sortedData[0]?.val || sortedData[0]?.areaNUm || sortedData[0]?.population_count || 1

        this.populationRanking = sortedData.slice(0, 4).map(item => ({
          name: item.areaName || item.address_name || item.name,
          value: item.val || item.areaNUm || item.population_count,
          percentage: Math.round((item.val || item.areaNUm || item.population_count) / maxValue * 100)
        }))
      } catch (error) {
        console.error('获取人员排名数据失败:', error)
      }
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .ranking-list {
      .ranking-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        padding: 8px 0;

        .rank-name {
          width: 120px;
          font-size: 28px;
          color: #dcefff;
        }

        .rank-bar {
          flex: 1;
          height: 12px;
          background: rgba(0, 0, 0, 0.3);
          border-radius: 6px;
          margin: 0 15px;
          position: relative;

          .bar-fill {
            height: 100%;
            background: linear-gradient(to right, #ff6b35, #f7931e);
            border-radius: 6px;
            transition: width 0.3s ease;
          }
        }

        .rank-value {
          width: 80px;
          text-align: right;
          font-size: 28px;
          font-family: DIN, DIN;
          font-weight: bold;
          background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
}
</style>
