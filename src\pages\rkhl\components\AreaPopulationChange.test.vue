<template>
  <div>
    <h2>测试 AreaPopulationChange 组件</h2>
    <AreaPopulationChange :currentLevel="currentLevel" />
    
    <div style="margin-top: 20px;">
      <button @click="changeLevel('重点场所')">重点场所</button>
      <button @click="changeLevel('商业区')">商业区</button>
      <button @click="changeLevel('居民区')">居民区</button>
    </div>
  </div>
</template>

<script>
import AreaPopulationChange from './AreaPopulationChange.vue'

export default {
  name: 'AreaPopulationChangeTest',
  components: {
    AreaPopulationChange,
  },
  data() {
    return {
      currentLevel: '重点场所',
    }
  },
  methods: {
    changeLevel(level) {
      this.currentLevel = level
    },
  },
}
</script>

<style scoped>
button {
  margin: 0 10px;
  padding: 8px 16px;
  background: #1677ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #0958d9;
}
</style>
