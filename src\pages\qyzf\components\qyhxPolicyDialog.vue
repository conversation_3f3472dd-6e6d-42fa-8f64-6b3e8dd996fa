<template>
  <el-dialog
    :top="winTop"
    ref="commomDialog"
    custom-class="custom-class-dialog"
    :show-close="false"
    :modal="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="visible"
    :modal-append-to-body="true"
    :append-to-body="true"
  >
    <div class="qyhxpolicy-container">
      <div class="dialog-top">
        <span>政策推荐</span>
        <i class="el-icon-close" style="float: right; cursor: pointer" @click="close"></i>
      </div>
      <div class="dialog-con" style="overflow: auto; height: 1445px; flex-wrap: wrap">
        <div class="qyhxPolicy-contentBack" v-for="(item, index) in zctjList" :key="index">
          <div class="firstTitle">{{ item.zcbt || '' }}</div>
          <div class="secondTitle" :title="item.zcyd">{{ item.zcyd }}</div>
          <div class="thirdTitle">
            <div class="inline">
              <div class="s-m-r-10">适用地区:{{ item.dsmc }}-{{ item.qxmc }}</div>
              <div></div>
            </div>
            <div class="inline">
              <div class="s-m-r-10">责任部门:{{ item.zrbm }}</div>
              <div></div>
            </div>
            <div class="inline">
              <div class="s-m-r-10">发文日期:{{ item.yfrq ? item.yfrq.split(' ')[0] : '' }}</div>
              <div></div>
            </div>
          </div>
          <div class="fourthTitle">
            <div class="inline">
              <img src="@/pages/qyzf/img/clock.png" alt="" />
              <div class="sbTime" v-if="item.jsrq != '1970-01-01'">申报时间:{{ item.ksrq }}至{{ item.jsrq }}</div>
              <div class="sbTime" v-else-if="item.ksrq != '1970-01-01'">申报时间:自{{ item.ksrq }}起</div>
              <div class="sbTime" v-else>暂无数据</div>
              <div></div>
            </div>
            <div style="display: flex; justify-content: space-between">
              <div style="display: flex; align-items: center; cursor: pointer" @click="showMoreFun(item)">
                <div class="more">{{ item.flag ? '收起' : '展开' }}</div>
                <img :src="item.flag ? require('@/pages/qyzf/img/top.png') : require('@/pages/qyzf/img/bottom.png')" />
              </div>
            </div>
          </div>
          <div v-show="item.flag" style="margin-top: 25px">
            <div>
              <div style="display: flex; align-items: center">
                <img src="@/pages/qyzf/img/Union.png" alt="" />
                <div style="font-size: 36px; color: #fff; margin-left: 10px">政策要点</div>
              </div>
              <div style="font-size: 32px; color: #bcc3cb; margin: 15px 0">{{ item.zcyd }}</div>
            </div>
            <div>
              <div style="display: flex; align-items: center">
                <img src="@/pages/qyzf/img/Union.png" alt="" />
                <div style="font-size: 36px; color: #fff; margin-left: 10px">要点解读</div>
              </div>
              <div style="font-size: 32px; color: #bcc3cb; margin: 15px 0">暂无数据</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 分页 -->
      <div class="data-page">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :total="total"
          :page-size="10"
          :current-page="currentPage"
        ></el-pagination>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import $ from 'jquery'
import axios from 'axios'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    qyhxGsbq: {
      type: [Array, String],
      default: '',
    },
    qyhxJbxxList: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      winTop: '40px',
      total: 0,
      page: 10,
      currentPage: 1,
      zctjList: [],
      allZclist: [],
    }
  },
  watch: {
    if(newVal) {
      let that = this
      this.$nextTick(() => {
        setTimeout(() => {
          that.winTop =
            (document.documentElement.clientHeight - $(that.$refs.commomDialog.$el).find('.el-dialog').height()) / 2 +
            'px'
        }, 100)
      })
    },
  },
  mounted() {
    let obj = []
    this.qyhxGsbq.forEach((el) => {
      obj.push(`"${el}"`)
    })
    let qxmc = this.qyhxJbxxList
    console.log(obj, qxmc,'筛选条件')
    this.$nextTick(() => {
      getCsdnInterface1('qyhx_qyxq_zctj', {
        bq: obj.join(','),
        qxmc: qxmc,
      }).then((res) => {
        this.zctjList = res.data.data
        this.zctjList.forEach((item) => {
          item['flag'] = false
        })
        this.total = this.zctjList.length
        this.allZclist = this.zctjList
        this.handleSizeChange(10)
      })
    })
  },
  methods: {
    showMoreFun(ele) {
      let index = this.zctjList.findIndex((o) => o.hqzcid == ele.hqzcid)
      this.zctjList[index].flag = !this.zctjList[index].flag
      this.$forceUpdate()
    },
    // 每页多少数量点击
    handleSizeChange(val) {
      this.page = val
      this.handleCurrentChange(1)
    },
    //分页点击
    handleCurrentChange(e) {
      let page = this.page
      this.zctjList = []
      this.currentPage = e
      if (e == 1) {
        this.zctjList = this.zctjList != [] ? this.allZclist.slice(0, page) : []
      } else {
        this.zctjList = this.zctjList != [] ? this.allZclist.slice(page * (e - 1), page * e) : []
      }
      this.total = this.allZclist.length
    },
    close() {
      this.$emit('close')
    },
  },
}
</script>

<style lang="less" scoped>
// 重置element-ui弹框
/deep/.el-dialog {
  width: 3000px;
  height: 1800px;
  // background: rgba(0, 15, 55, 0.9);
  background: transparent;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  margin-left: 2350px !important;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}
.title-container .close-btn {
  float: right;
  margin: 15px;
  cursor: pointer;
  font-size: 36px;
  color: white;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

p {
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 2160px;
  background-color: #00000065;
  display: flex;
  justify-content: center;
  align-items: center;
}

::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

::-webkit-scrollbar-thumb {
  border-radius: 6px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}

.qyhxpolicy-container {
  width: 2200px;
  height: 1800px;
  padding: 90px 65px 50px;
  box-sizing: border-box;
  background: url('@/pages/qyzf/img/dialog-bg.png');
  background-size: 100% 100%;
  overflow: hidden;
}

/* 头部 */
.dialog-top {
  font-size: 50px;
  font-weight: bold;
  color: #ffffff;
}

.dialog-top > span:first-child {
  background: linear-gradient(0deg, #ffcc00 0.4150390625%, #ffffff 99.5849609375%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.reset-box {
  display: inline-block;
  font-size: 32px;
  letter-spacing: 2px;
  color: #fff !important;
  background: linear-gradient(180deg, #17aee0 0%, #0166a6 100%);
  padding: 0 20px;
  margin-left: 1600px;
  cursor: pointer;
}

/* 内容 */
.dialog-con {
  width: 100%;
  height: 1550px;
  padding: 20px;
  margin-top: 30px;
  box-sizing: border-box;
}

.select-box {
  width: 100%;
  height: 500px;
  border-bottom: 2px solid #00c0ff66;
  position: relative;
  overflow: hidden;
  overflow-y: auto;
  padding: 0 80px 20px 20px;
  box-sizing: border-box;
}

.select-box::before {
  content: '';
  width: 20px;
  height: 20px;
  /* background-image: url('/static/images/icon/icon1.png'); */
  background-size: 100% 100%;
  position: absolute;
  left: 0;
  bottom: 0;
}

.select-data-box {
  width: 100%;
  height: 970px;
  margin-top: 50px;
}

/* 筛选区域的样式 */
.select-item {
  width: 100%;
  margin-bottom: 20px;
  color: #fff;
  font-size: 32px;
  display: flex;
  justify-content: space-between;
}

.select-name {
  width: 200px;
  text-align: right;
  padding-right: 50px;
  box-sizing: border-box;
}

.select-list {
  flex: 1;
}

.show-more {
  background: linear-gradient(180deg, #17aee0 0%, #0166a6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-left: 50px;
}

.click-color {
  background: linear-gradient(180deg, #17aee0 0%, #0166a6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.click-color2 .el-input__inner {
  color: #0166a6 !important;
}

.ul-list {
  display: flex;
  flex-wrap: wrap;
}

.ul-list > li {
  margin-right: 50px;
  margin-bottom: 10px;
  /* cursor: not-allowed; */
  /* 暂时无数据不支持点击 */
}

.ul-list > li:last-child {
  margin-right: 0;
}

/* 更多 */
.remove-more {
  height: 50px;
  overflow: hidden;
}

/* 企业数据 */
.data-sum {
  width: 100%;
  font-size: 28px;
  margin-bottom: 6px;
  color: #fff;
  font-weight: 400;
  padding-left: 20px;
  box-sizing: border-box;
}

.data-list {
  width: 100%;
  height: 800px;
  color: #fff;
  font-size: 32px;
  margin-top: 20px;
  overflow-y: auto;
}

::v-deep .data-page {
  width: 100%;
  height: 100px;
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  /* 新的分页样式 */
  .el-pagination.is-background .btn-next,
  .el-pagination.is-background .btn-prev,
  .el-pagination.is-background .el-pager li {
    background: #5f7b96;
    color: #fff;
    font-size: 34px;
    font-weight: normal;
    height: 60px;
    line-height: 58px;
    box-sizing: border-box;
  }

  .el-pager li.active + li {
    border-left: 1px solid transparent !important;
  }

  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background: #0166a6;
    border-radius: 3px 3px 3px 3px;
    height: 60px;
    line-height: 60px;
    box-sizing: border-box;
  }

  .el-pager li {
    background: #5f7b96;
    padding: 0 20px;
    border: 1px solid transparent;
    box-sizing: border-box;
  }

  .el-pagination .btn-next .el-icon,
  .el-pagination .btn-prev .el-icon {
    font-size: 30px;
  }

  .el-pagination .btn-next,
  .el-pagination .btn-prev {
    width: 60px;
  }

  /* 分页- */
  .el-pagination__total {
    color: #fff;
    font-size: 32px !important;
    margin: 0;
    padding-right: 20px;
  }

  /* 分页-多少页 */
  .el-pagination__sizes .el-input .el-input__inner {
    font-size: 32px;
    background-color: transparent;
    border-color: #6f788a;
    height: 60px !important;
    line-height: 60px;
    color: #fff;
  }

  .el-pagination .el-select .el-input .el-input__inner {
    padding-right: 51px;
  }

  .el-select .el-input .el-select__caret {
    font-size: 40px;
    color: #6f788a;
    transform: rotate(0);
    padding-top: 10px;
  }

  .el-select .el-input .el-select__caret.is-reverse {
    transform: rotate(180deg);
    margin-top: -10px;
  }

  .el-pagination .el-select .el-input {
    width: 250px;
    margin: 0;
  }

  .el-pagination button,
  .el-pagination span:not([class*='suffix']) {
    height: 60px;
    line-height: 60px;
  }

  .el-pagination .el-input__suffix {
    right: 15px;
  }

  .el-select-dropdown__item {
    font-size: 32px;
    background-color: transparent;
    color: #fff;
    border-color: #6f788a;
    margin-bottom: 10px;
  }

  .el-select-dropdown {
    background-color: #041330;
    border-color: #e4e7ed4f;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: transparent;
  }

  .el-popper[x-placement^='bottom'] .popper__arrow {
    display: none;
  }

  .el-pagination button,
  .el-pagination span:not([class*='suffix']) {
    font-size: 32px;
    color: #fff;
  }

  .el-pagination__editor.el-input .el-input__inner {
    height: 60px;
    font-size: 32px;
    background-color: transparent;
    color: #fff;
    border-color: #6f788a;
  }

  .el-pagination__editor.el-input {
    width: 100px;
    height: 60px;
    margin: 0 10px;
  }
}

/* 更多筛选样式 */
.change-box {
  display: flex;
}

.box1 {
  width: 280px;
  margin-right: 50px;
}

.box1 .el-input__inner {
  height: auto;
  font-size: 32px;
  background-color: transparent;
  color: #fff;
  border-color: transparent !important;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: 0 30px 0 0;
  text-align: center;
}

.box1 .el-select .el-input.is-focus .el-input__inner {
  border-color: transparent;
}

.box1 .el-select .el-input__inner:focus {
  border-color: transparent;
}

.box1 .el-input__inner:hover {
  border-color: transparent;
}

.box1 .el-input__icon:before {
  content: '\e78f';
  color: #fff;
}

.box1 .el-select .el-input {
  font-size: 40px;
}

.box1 .el-cascader .el-input .el-icon-arrow-down {
  font-size: 40px;
  padding-top: 10px;
}

.el-cascader__dropdown {
  background: transparent;
}

.box1 .el-avatar,
.el-cascader-panel,
.el-radio,
.el-radio--medium.is-bordered .el-radio__label,
.el-radio__label {
  font-size: 32px;
}

.el-cascader__dropdown .el-checkbox__inner {
  width: 25px;
  height: 25px;
  margin-right: 10px;
}

.el-cascader-node {
  margin-bottom: 10px;
  color: #fff;
}

.el-cascader-node:not(.is-disabled):focus,
.el-cascader-node:not(.is-disabled):hover {
  background: transparent;
}

.el-checkbox__inner::after {
  width: 10px;
  height: 20px;
  left: 7px;
  top: -3px;
}

.el-cascader__tags .el-tag {
  background: transparent;
  margin: 0;
}

.el-tag.el-tag--info {
  background-color: transparent;
  color: #fff;
  height: 100% !important;
}

.el-tag {
  font-size: 30px;
}

.el-tag .el-icon-close {
  width: 32px;
  height: 32px;
  font-size: 32px;
  text-align: center;
  line-height: 32px;
}

.el-cascader__tags .el-tag > span {
  height: 100%;
  line-height: 40px;
}

.count {
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 40px;
}

.zscqBox:nth-child(1) {
  width: 267px;
  height: 155px;
  background: linear-gradient(180deg, #1cf5cd 0%, rgba(28, 245, 205, 0) 100%);
  text-align: center;
}

.zscqBox:nth-child(2) {
  width: 267px;
  height: 155px;
  background: linear-gradient(180deg, #1e76a5 0%, rgba(30, 118, 165, 0) 100%);
  text-align: center;
}

.zscqBox:nth-child(3) {
  width: 267px;
  height: 155px;
  background: linear-gradient(180deg, #ffb151 0%, rgba(255, 177, 81, 0) 100%);
  text-align: center;
}

.cqNum {
  margin: auto;
  width: 64px;
  height: 69px;
  font-size: 52px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: 700;
  line-height: 69px;
  background: linear-gradient(360deg, #91f4e1 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.cqName {
  color: #fff;
  font-size: 32px;
}

.cqName span {
  color: #909399;
}

.jyfx {
  color: #cde7ff;
}

.jyfxActive {
  color: #ffffff;
  border-bottom: 1px solid #ffffff;
}

.flow-icon {
  top: 25px;
}

.flow-icon1 {
  top: 18px;
  transform: rotateX(180deg);
}

.ul {
  height: 64px !important;
  background: none;
  border-color: transparent !important;
  background: linear-gradient(180deg, #17aee0 0%, #17aee0 0%, #0c8ac3 50%, #0166a6 100%);
  border: none;
  border-radius: 3px 3px 3px 3px;
}

.back {
  width: 120px;
  height: 120px;
  position: absolute;
  top: 255px;
  left: 4750px;
  background-image: url('@/pages/qyzf/img/back.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  z-index: 910;
  cursor: pointer;
}

.qyhxPolicy-contentBack {
  background-color: #102647;
  margin-bottom: 20px;
  padding: 20px 20px;
  box-sizing: border-box;
  width: 100%;
}

.firstTitle {
  font-size: 40px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  cursor: pointer;
  color: #ffffff;
}

.secondTitle {
  width: 1900px;
  font-size: 32px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #cfd4da;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  margin: 15px 0;
}

.thirdTitle {
  font-size: 32px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #bcc3cb;
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.fourthTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28px;
  color: #91d1f4;
}

.more {
  font-size: 28px;
  margin: 0 10px 0 40px;
}

.sbTime {
  margin: 0 10px;
}

.inline {
  display: flex;
  align-items: center;
}

.el-select .el-input .el-select__caret {
  padding: 0 !important;
}
</style>
