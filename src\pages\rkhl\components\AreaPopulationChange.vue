<template>
  <div class="chart-item large">
    <SubTitle title="人口变化情况" />
    <div class="chart-container">
      <!-- 时间维度切换 -->
      <div class="time-controls">
        <OptionsSwitcher
          v-model="hourORfen"
          :options="timeOptions"
          @change="changeEchart"
        />
      </div>

      <!-- 天维度图表 -->
      <div v-show="hourORfen === '天'" id="tianEch" class="chart"></div>

      <!-- 时维度图表和时间选择器 -->
      <div v-show="hourORfen === '时'" class="hour-container">
        <ul class="timePicker" v-show="false">
          <li
            @click="timeChangeFun('今日')"
            :class="{'timePicker_active': inputText === '今日'}"
          >今日</li>
          <li
            v-for="item of 24"
            :key="item"
            @click="timeChangeFun(item - 1)"
            :class="{'timePicker_active': Number(inputText.replace('时', '')) === item - 1}"
          >
            {{ (item - 1).toString().padStart(2, '0') }}时
          </li>
        </ul>
        <div id="lineEcharts" class="chart"></div>
      </div>


    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import OptionsSwitcher from '@/components/OptionsSwitcher.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'AreaPopulationChange',
  components: {
    SubTitle,
    OptionsSwitcher,
  },
  props: {
    currentLevel: {
      type: String,
      default: '重点场所',
    },
  },
  data() {
    return {
      hourORfen: '时',
      hourTit: '24小时',
      showTimePicker: false,
      inputText: '今日',
      startName: '默认区域',
      code: null,
      timeOptions: [
        { label: '天', value: '天' },
        { label: '时', value: '时' }
      ],
    }
  },
  mounted() {
    this.startName = this.currentLevel
    // 初始化默认显示时维度
    this.changeEchart(this.hourORfen)
  },
  watch: {
    currentLevel: {
      handler(newLevel) {
        this.handleLevelChange(newLevel)
      },
      immediate: true,
    },
  },
  methods: {
    // 处理级别变化
    handleLevelChange(level) {
      this.startName = level
      // 重新初始化数据
      this.changeEchart(this.hourORfen)
    },

    // 时间变化处理
    timeChangeFun(time) {
      this.showTimePicker = false
      this.inputText = time.constructor === String ? time : time.toString().padStart(2, '0') + '时'

      if (this.inputText === '今日') {
        // 获取今日数据
        this.getTodayData()
      } else {
        // 获取指定小时数据
        this.getHourData(this.inputText.replace('时', ''))
      }
    },

    // 获取今日数据
    async getTodayData() {
      try {
        const nowData = this.$moment().subtract(0, 'days').format('YYYY-MM-DD')
        const res = await getCsdnInterface1('rkztRight020', {
          code: this.code,
          insert_time: nowData
        })
        const timeData = res.data.filter((item) => {
          return item.data0.indexOf(nowData) > -1
        })
        this.xyLineFun('lineEcharts', timeData, 'time')
      } catch (error) {
        console.error('获取今日数据失败:', error)
        this.renderMockHourData()
      }
    },

    // 获取小时数据
    async getHourData(hours) {
      try {
        const res = await getCsdnInterface1('csrk_xscx', {
          code: this.code,
          hours: hours
        })
        this.xyLineFun('lineEcharts', res.data, 'data')
      } catch (error) {
        console.error('获取小时数据失败:', error)
        this.renderMockHourData()
      }
    },

    // 时间维度切换
    async changeEchart(type) {
      this.hourORfen = type

      if (type === '天') {
        this.hourTit = '近3月'
        try {
          const res = await getCsdnInterface1('csrk_zdqy_3month', {
            typeCode: this.code
          })
          this.lineDayEch('tianEch', res.data)
        } catch (error) {
          console.error('获取天数据失败:', error)
          this.renderMockDayData()
        }
      } else {
        this.hourTit = '24小时'
        this.timeChangeFun(this.inputText)
      }
    },

    // 渲染天数据图表
    lineDayEch(id, data) {
      const chartDom = document.getElementById(id)
      if (!chartDom) return

      this.$echarts.dispose(chartDom)
      const chart = this.$echarts.init(chartDom)

      const legends = ['人口数量', '人口聚集预警']
      const xdata = data.map((a) => a.time.slice(5))

      const option = {
        grid: {
          left: '5%',
          right: '6%',
          top: '15%',
          bottom: '1%',
          containLabel: true,
        },
        legend: {
          data: legends,
          top: '4%',
          itemWidth: 12,
          icon: 'circle',
          itemHeight: 12,
          itemGap: 30,
          textStyle: {
            color: '#fff',
            fontSize: 26,
            fontFamily: 'SourceHanSansCN-Medium',
          },
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          textStyle: {
            fontSize: 26,
            color: '#fff',
          },
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
        },
        xAxis: {
          type: 'category',
          data: xdata,
          axisLabel: {
            color: '#fff',
            fontSize: 22,
          },
          axisLine: {
            lineStyle: {
              color: '#fff',
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 22,
          },
          axisLine: {
            lineStyle: {
              color: '#fff',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
            },
          },
        },
        series: [
          {
            name: legends[0],
            type: 'line',
            symbolSize: 5,
            itemStyle: {
              color: '#00C0FF',
              borderColor: '#00C0FF',
              borderWidth: 1,
            },
            lineStyle: {
              width: 5,
            },
            data: data.map((a) => a.individual_value),
          },
          {
            name: legends[1],
            data: data.map((a) => a.value || 0),
            type: 'line',
            smooth: true,
            showSymbol: true,
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: {
              color: 'red',
              width: 3,
            },
            itemStyle: {
              color: 'red',
            },
          }
        ],
      }

      chart.setOption(option)
    },





    // 模拟数据渲染方法
    renderMockDayData() {
      const mockData = [
        { time: '2024-01-01', individual_value: 1200, value: 5 },
        { time: '2024-01-02', individual_value: 1350, value: 3 },
        { time: '2024-01-03', individual_value: 1100, value: 8 },
        { time: '2024-01-04', individual_value: 1450, value: 2 },
        { time: '2024-01-05', individual_value: 1300, value: 6 },
      ]
      this.lineDayEch('tianEch', mockData)
    },

    renderMockHourData() {
      // 模拟小时数据渲染
      const mockData = Array.from({ length: 24 }, (_, i) => ({
        time: i.toString().padStart(2, '0') + ':00',
        value: Math.floor(Math.random() * 1000) + 500
      }))
      this.xyLineFun('lineEcharts', mockData, 'data')
    },

    // 小时数据线图
    xyLineFun(domId, data, type) {
      const chartDom = document.getElementById(domId)
      if (!chartDom) return

      this.$echarts.dispose(chartDom)
      const chart = this.$echarts.init(chartDom)

      const xData = type === 'time' ?
        data.map(item => item.data0.split(' ')[1]) :
        data.map(item => item.time)
      const yData = data.map(item => item.value || item.individual_value)

      const option = {
        grid: {
          left: '5%',
          right: '5%',
          top: '10%',
          bottom: '10%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          textStyle: { color: '#fff' },
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: { color: '#fff' },
          axisLine: { lineStyle: { color: '#fff' } },
        },
        yAxis: {
          type: 'value',
          axisLabel: { color: '#fff' },
          axisLine: { lineStyle: { color: '#fff' } },
          splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
        },
        series: [
          {
            type: 'line',
            data: yData,
            smooth: true,
            lineStyle: { color: '#00c0ff', width: 2 },
            itemStyle: { color: '#00c0ff' },
            areaStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(0, 192, 255, 0.3)' },
                { offset: 1, color: 'rgba(0, 192, 255, 0)' },
              ]),
            },
          },
        ],
      }

      chart.setOption(option)
    },


  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  &.large {
    flex: 1.5;
  }

  .chart-container {
    position: relative;

    .time-controls {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 10;
    }

    .hour-container {
      position: relative;

      .input_box {
        cursor: pointer;
        width: 200px;
        height: 35px;
        border-radius: 20px;
        color: #fff;
        background-color: #132c4ed0;
        border: 1px solid #359cf8;
        position: absolute;
        right: 20px;
        top: 50px;
        text-align: center;
        z-index: 888;
        font-size: 16px;
      }

      .timePicker {
        font-size: 14px;
        position: absolute;
        right: 10px;
        top: 90px;
        list-style: none;
        width: 310px;
        display: flex;
        color: #ccc;
        flex-wrap: wrap;
        background-color: #132c4eab;
        border: 1px solid #359cf8;
        z-index: 888;
        padding: 10px;
        border-radius: 8px;

        li {
          width: max-content;
          padding: 4px 8px;
          margin: 2px;
          border: 1px solid transparent;
          cursor: pointer;
          border-radius: 4px;
          transition: all 0.3s ease;

          &.timePicker_active {
            border: 1px solid #fff;
            color: #359cf8;
            background: rgba(53, 156, 248, 0.2);
          }

          &:hover {
            border: 1px solid #359cf8;
            color: #fff;
          }
        }
      }
    }



    .chart {
      width: 100%;
      height: 400px;
    }
  }
}
</style>
