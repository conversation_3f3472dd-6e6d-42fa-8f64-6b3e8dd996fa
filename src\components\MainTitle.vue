<template>
  <div class="main-title" :class="sizeClass">
    <div class="title-text">
      <div>{{ title }}</div>
      <div class="title-extra" v-if="$slots.extra">
        <slot name="extra"></slot>
      </div>
    </div>
    <div class="title-right" v-if="$slots.right">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MainTitle',
  props: {
    title: {
      type: String,
      required: true,
      default: '',
    },
    size: {
      type: String,
      default: 'normal',
      validator: (value) => ['normal', 'large'].includes(value),
    },
  },
  computed: {
    sizeClass() {
      return `main-title--${this.size}`
    },
  },
}
</script>

<style lang="less" scoped>
.main-title {
  width: 100%;
  height: 110px;
  background-size: 100% 100%;
  padding-left: 100px;
  position: relative;
  padding-right: 0px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &--normal {
    background-image: url('@/assets/img/main-title-m.png');
  }

  &--large {
    background-image: url('@/assets/img/main-title-l.png');
  }
}

.title-text {
  line-height: 110px;
  font-size: 50px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  display: flex;
  justify-content: space-between;
}

.title-extra {
  display: flex;
  align-items: center;
}

.title-right {
  display: flex;
  align-items: center;
  padding-right: 100px;
  position: absolute;
  right: 0;
  top: 0;
  line-height: 110px;
}
</style>
